<?php

namespace App\Livewire;

use App\Models\Client;
use App\Models\Customer;
use App\Models\ProductSpecification;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;

class SpecialPriceSelector extends Component implements HasForms, HasTable
{
    use CCTraitColumn;
    use InteractsWithTable;
    use InteractsWithForms;

    public Customer|Client $record;



    public function mount(Customer|Client $record)
    {
        $this->record = $record;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(function ()
            {
                return ProductSpecification::query()
                    ->whereNotIn(
                        'product_specifications.id',
                        function ($query) {
                            $query->select('product_specification_id')
                                ->from('specializable_product_specifications')
                                ->where('specializable_type', get_class($this->record))
                                ->where('specializable_id', $this->record->id);
                        }
                    );
            })
            ->columns([

                self::getColumnTextCopyable('sku')
                    ->label('產品規格型號')
                    ->searchable(true,function($query, $search) {
                        return $query->where('sku', 'like', "%{$search}%");
                    },true),

                self::getColumnTranslation('title')
                    ->label('產品規格名稱'),

                TextColumn::make('listing_price')
                    ->label('建議售價'),

                TextColumn::make('selling_price')
                    ->label('銷售價一')
                    ->abbr('B2B 官網的銷售價四', asTooltip: true),

                TextColumn::make('selling_price2')
                    ->label('銷售價二')
                    ->abbr('B2B 官網的銷售價二', asTooltip: true),

                TextColumn::make('selling_price3')
                    ->label('銷售價三')
                    ->abbr('B2B 官網的銷售價三', asTooltip: true),

                TextColumn::make('selling_price4')
                    ->label('銷售價四')
                    ->abbr('B2C 官網的銷售價四', asTooltip: true)

            ])
            ->filters([

                Filter::make('tree')
                    ->form([

                        ServiceProductCategory::getColumnSelectTree('product_category_id', relation: 'category')
                            ->required(),

                    ])
                    ->query(function ($query, array $data) {
                        return $query->when(
                            filled($data['product_category_id']) && $data['product_category_id'] != -1,
                            function ($query) use ($data) {
                                $category = ProductCategory::find($data['product_category_id']);
                                if (!$category) {
                                    return $query;
                                }

                                $categoryIds = $category->descendantsWithSelf()->pluck('id');

                                return $query->whereHas('product', function ($productQuery) use ($categoryIds) {
                                    $productQuery->whereIn('product_category_id', $categoryIds);
                                });
                            }
                        );
                    })


            ])
            ->actions([])
            ->bulkActions([
                BulkAction::make('select')
                    ->label('選擇')
                    ->icon('heroicon-o-check')
                    ->action(function (Collection $records) {
                        $attachData = $records->mapWithKeys(function ($spec) {
                            return [
                                $spec->id => [
                                    'special_price' => $spec->{$this->record->price_type},
                                ],
                            ];
                        });
                        $this->record->specialProductSpecifications()->attach($attachData);
                    })
                    ->after(function ()
                    {
                        Notification::make('success')
                            ->title("新增成功")
                            ->success()
                            ->color('success')
                            ->send();
                    }),
            ])
            ->paginated([
                10, 25, 50, 100
            ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('special_price')
                    ->label('專屬價格')
                    ->numeric()
                    ->minValue(0)
                    ->required(),
            ]);
    }


    public function render(): View
    {
        return view('livewire.product-specifications.special-price-selector-view');
    }
}
