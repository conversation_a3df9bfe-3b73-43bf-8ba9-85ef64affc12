<?php

namespace App\Console\Commands;

use Database\Seeders\ClientSeeder;
use Illuminate\Console\Command;

class ImportClientsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import-clients';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import clients from CSV file';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Starting client import process...');

        try {
            $seeder = new ClientSeeder();
            $seeder->setCommand($this);
            $seeder->run();

            $this->info('Client import completed successfully!');
        } catch (\Exception $e) {
            $this->error('Error during client import: ' . $e->getMessage());
        }
    }
}
