<?php

namespace App\Console\Commands;

use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use <PERSON>chenorg\BaseFilamentPlugin\Models\Client;
use Illuminate\Console\Command;
use App\Services\ErpAuthService;

class UpdateAuthCommand extends Command
{
    protected $signature = 'update-auth';
    protected $description = '更新所有客戶資料';

    public function handle()
    {
        $this->update('B2B');
        $this->update('B2C');
    }

    /**
     * @param $type
     * @return void
     */
    public function update($type): void
    {
        $this->info("開始更新 {$type} 客戶資料...");

        try {
            $erpAuthService = new ErpAuthService();
            $count = 0;

            if($type === 'B2C'){
                Customer::query()
                    ->select('id', 'code')
                    ->whereNotNull('code')
                    ->chunkById(100, function ($auth) use ($type,$erpAuthService, &$count) {
                        $authCodes = $auth->pluck('code')->toArray();
                        $this->info("正在處理第 " . ($count + 1) . " 組 {$type} 客戶");
                        $erpAuthService->updateFromErp($authCodes);
                        $count++;
                    });
            }

            if($type === 'B2B'){
                Client::query()
                    ->select('id', 'code')
                    ->whereNotNull('code')
                    ->chunkById(100, function ($auth) use ($type,$erpAuthService, &$count) {
                        $authCodes = $auth->pluck('code')->toArray();
                        $this->info("正在處理第 " . ($count + 1) . " 組 {$type} 客戶");
                        $erpAuthService->updateFromErp($authCodes);
                        $count++;
                    });
            }


            if ($count === 0) {
                $this->warn("沒有找到任何 {$type} 客戶");
            } else {
                $this->info("客戶更新完成！共處理 {$count} 組 {$type} 客戶");
            }

        } catch (\Exception $e) {
            $this->error("更新 {$type} 客戶時發生錯誤：" . $e->getMessage());
        }
    }
}
