<?php

namespace App\Console\Commands;

use App\Services\ErpService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;

class UpdateProductsCommand extends Command
{
    protected $signature = 'update-products';
    protected $description = '更新所有可同步的產品資料';

    public function handle()
    {
        $this->info('開始更新可同步的產品資料...');

        try {
            $erpService = new ErpService();
            $count = 0;

            Log::info('UpdateProductsCommand 處理中');

            Product::query()
                ->select('id', 'part_number')
                ->where('syncable', '=',true)
                ->chunkById(100, function ($products) use ($erpService, &$count) {
                    $productCodes = $products->pluck('part_number')->toArray();
                    $this->info("正在處理第 " . ($count + 1) . " 組產品");
                    $erpService->updateProducts($productCodes);
                    $count++;
                });

            if ($count === 0) {
                $this->warn('沒有找到任何可同步的產品');
            } else {
                $this->info("可同步產品更新完成！共處理 {$count} 組產品");
            }

        } catch (\Exception $e) {
            $this->error('更新產品時發生錯誤：' . $e->getMessage());
        }
    }
}
