<?php

namespace App\Console\Commands;

use Database\Seeders\CustomerSeeder;
use Illuminate\Console\Command;

class ImportCustomersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import-customers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import customers from CSV file';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Starting customers import process...');

        try {
            $seeder = new CustomerSeeder();
            $seeder->setCommand($this);
            $seeder->run();

            $this->info('customer import completed successfully!');
        } catch (\Exception $e) {
            $this->error('Error during customer import: ' . $e->getMessage());
        }
    }
}
