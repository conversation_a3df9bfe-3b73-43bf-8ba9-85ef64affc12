<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ArticleResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\TagService;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephen<PERSON>org\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormDate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class ArticleResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormDate;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitAction;

    protected static ?string $model = Article::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';


    public static function getNavigationGroup(): ?string
    {
        return '作品集管理';
    }

    /**
     * @return string|null
     */
    public static function getLabel(): ?string
    {
        return '作品集';
    }

//    public static function getNavigationBadge(): ?string
//    {
//        $count = CountService::getArticleCount();
//        $unit = CountService::getUnit('article');
//
//        return "$count $unit";
//    }

    public static function form(Form $form): Form
    {
        $articleLabel = '作品集';
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        self::getFormToggle('status')
                            ->label('啟用/停用'),
                        Select::make('article_category_id')
                            ->label("所屬 {$articleLabel} 類別")
                            ->relationship('category', 'key')
                            ->getOptionLabelFromRecordUsing(function ($record) {
                                return $record->translations
                                    ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                                    ->first()->title;
                            })
                            ->columnSpanFull()
                            ->required(),
                        self::getFormKey()
                            ->reactive()
                            ->debounce(500)
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('slug', Str::slug($state));
                            }),

                        self::getFormToggle('is_hottest')
                            ->label('(是/否)熱門'),

                        self::getFormToggle('is_newest')
                            ->label('(是/否)最新'),

                        self::getFormSort(),
                        self::getFormSlug(),
                        TagService::getArticleTagSelectForm(),
                    ]),

                self::getFormTime(),

                self::getFormSectionImage('cover')
                    ->heading('封面圖片'),

                self::getFormSectionImage('background')
                    ->heading('背景圖片'),

                self::getTabLanguage([
                    self::getFormTitle(),
                    TextInput::make("author")
                        ->label('作者')
                        ->columnSpanFull()
                        ->placeholder('請輸入作者名稱'),
                    self::getFormEditor('content')
                        ->label('內容 - 圖文編輯器')
                        ->placeholder('請輸入內容')
                        ->columnSpan('full'),
                    self::getFormDescription(),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTranslation('title', false, 'category')
                    ->label('作品集類別'),
                self::getColumnTranslation('title', true)
                    ->label('作品集標題'),
                self::getColumnTextKey(),
                self::getColumnTextTag()->label('作品集標籤'),
                self::getColumnImage('cover', '封面圖片'),
                self::getColumnImage('background', '背景圖片'),
                self::getColumnTextIsHottest(),
                self::getColumnTextIsNewest(),
                self::getColumnTextStatus(),
                self::getColumnTextInputSort(),
                ...self::getColumnsTextPeriod(),
            ])
            ->filters([
                SelectFilter::make('article_category_id')
                    ->preload()
                    ->relationship('category','key')
                    ->getOptionLabelFromRecordUsing(function ($record) {
                        return $record->translations
                            ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                            ->first()->title;
                    })
                    ->label('作品集類別')
                    ->searchable(),
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ArticleResource\RelationManagers\TagsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListArticles::route('/'),
            'create' => Pages\CreateArticle::route('/create'),
            'edit' => Pages\EditArticle::route('/{record}/edit'),
            'view' => Pages\ViewArticle::route('/{record}'),
        ];
    }


    public static function canAccess(): bool
    {
        return config('cs.article_visible') && Gate::allows('view-any', Article::class);
    }


}
