<?php

namespace App\Filament\Resources;

use App\Enum\EnumArea;
use App\Enum\EnumGender;
use App\Enum\EnumInvoiceIssueMethod;
use App\Enum\EnumInvoiceTaxType;
use App\Enum\EnumPriceType;
use App\Filament\Resources\ClientResource\RelationManagers\SpecialPriceRelationManager;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource as BaseClientResource;
use Stephenchenorg\BaseFilamentPlugin\Service\PasswordService;

class ClientResource extends BaseClientResource
{
    protected static ?string $model = \App\Models\Client::class;

    public static function form(Form $form): Form
    {

        return $form
            ->schema([

                // 業務資訊區塊
                Forms\Components\Section::make('業務資訊')
                    ->icon('heroicon-o-briefcase')
                    ->schema([
                        Forms\Components\Select::make('sale_code')
                            ->label('業務員代號')
                            ->relationship('sale', modifyQueryUsing: fn($query) => $query->where('is_enabled', true))
                            ->getOptionLabelFromRecordUsing(function ($record) {
                                return $record->code . ' - ' . $record->name;
                            })
                            ->searchable()
                            ->preload()
                            ->placeholder('請選擇業務員'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('基本資料')
                    ->schema([
                        TextInput::make('code')
                            ->label('客戶代號')
                            ->maxLength(30)
                            ->unique(ignoreRecord: true)
                            ->disabled(),

                        TextInput::make('company_name')
                            ->label('客戶名稱')
                            ->required()
                            ->maxLength(60)
                            ->autofocus()
                            ->disabled(),

                        TextInput::make('name')
                            ->label('客戶簡稱')
                            ->required()
                            ->maxLength(50)
                            ->autofocus()
                            ->disabled(),

                        TextInput::make('email')
                            ->label('電子郵件')
                            ->required()
                            ->email()
                            ->maxLength(70)
                            ->unique(ignoreRecord: true)
                            ->disabled(),

                        (new PasswordService())->getPassword()->disabled(),

                        (new PasswordService())->getPasswordConfirmation()->disabled(),

                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('電子郵件驗證時間')
                            ->displayFormat('Y-m-d H:i:s')
                            ->disabled(),
                    ]),


                Forms\Components\Section::make('地址資訊')
                    ->schema([
                        TextInput::make('address')
                            ->label('客戶地址')
                            ->maxLength(255)
                            ->disabled(),

                        TextInput::make('invoice_address')
                            ->label('發票地址')
                            ->maxLength(255)
                            ->disabled(),

                        TextInput::make('zipcode')
                            ->label('郵遞區號')
                            ->maxLength(10)
                            ->disabled(),
                    ]),

                Forms\Components\Section::make('聯絡資訊')
                    ->schema([
                        TextInput::make('phone1')
                            ->label('客戶電話1')
                            ->maxLength(20)
                            ->disabled(),

                        TextInput::make('phone1_extension')
                            ->label('電話1分機')
                            ->maxLength(10)
                            ->disabled(),

                        TextInput::make('phone2')
                            ->label('客戶電話2')
                            ->maxLength(20)
                            ->disabled(),

                        TextInput::make('phone2_extension')
                            ->label('電話2分機')
                            ->maxLength(10)
                            ->disabled(),

                        TextInput::make('fax')
                            ->label('傳真電話')
                            ->maxLength(20)
                            ->disabled(),

                        TextInput::make('mobile')
                            ->label('手機')
                            ->maxLength(20)
                            ->disabled(),

                        TextInput::make('contact_person')
                            ->label('聯絡人')
                            ->maxLength(50)
                            ->disabled(),
                    ]),

                Forms\Components\Section::make('稅務和發票資訊')
                    ->schema([
                        TextInput::make('vat')
                            ->label('統一編號')
                            ->maxLength(20)
                            ->disabled(),
                    ]),
                // 網站資訊區塊
                Forms\Components\Section::make('網站資訊')
                    ->icon('heroicon-o-globe-alt')
                    ->schema([
                        Forms\Components\TextInput::make('web_account')
                            ->label('網路登錄帳號')
                            ->maxLength(30)
                            ->placeholder('請輸入網路登錄帳號')
                            ->disabled(),
                    ])
                    ->columns(2),

                // 價格設定區塊
                Forms\Components\Section::make('價格設定')
                    ->icon('heroicon-o-currency-dollar')
                    ->schema([
                        Forms\Components\Select::make('price_type')
                            ->label('售價類型')
                            ->options(EnumPriceType::getOptions())
                            ->required()
                            ->disabled(),
                    ])
                    ->columns(2),

                // 運費設定區塊
                Forms\Components\Section::make('運費設定')
                    ->icon('heroicon-o-truck')
                    ->schema([
                        Forms\Components\TextInput::make('basic_shipping_fee')
                            ->label('基本運費')
                            ->numeric()
                            ->minValue(0)
                            ->step(0.01)
                            ->placeholder('100')
                            ->disabled(),
                        Forms\Components\TextInput::make('free_shipping_threshold')
                            ->label('免運門檻金額')
                            ->numeric()
                            ->minValue(0)
                            ->step(0.01)
                            ->placeholder('1000')
                            ->disabled(),
                    ])
                    ->columns(2),


                // 郵件設定區塊
                Forms\Components\Section::make('郵件設定')
                    ->icon('heroicon-o-envelope')
                    ->schema([
                        Forms\Components\Toggle::make('email_statement')
                            ->label('EMail對帳單')
                            ->disabled(),
                    ])
                    ->columns(2),

                // 個人資訊區塊
                Forms\Components\Section::make('個人資訊')
                    ->icon('heroicon-o-user')
                    ->schema([
                        Forms\Components\TextInput::make('id_number')
                            ->label('身份證號')
                            ->maxLength(20)
                            ->placeholder('請輸入身份證號')
                            ->disabled(),
                        Forms\Components\DatePicker::make('birthday')
                            ->label('出生日期')
                            ->displayFormat('Y/m/d')
                            ->disabled(),
                        Forms\Components\Select::make('gender')
                            ->label('性別')
                            ->options(EnumGender::getOptions())
                            ->default(0)
                            ->disabled(),
                        Forms\Components\TextInput::make('bonus_points')
                            ->nullable()
                            ->default(0)
                            ->label('紅利點數')
                            ->numeric()
                            ->disabled(),
                        Forms\Components\TextInput::make('pos_bonus')
                            ->nullable()
                            ->default(0)
                            ->label('POS紅利點數')
                            ->numeric()
                            ->disabled(),
                    ])
                    ->columns(2),

                // 發票和付款方式區塊
                Forms\Components\Section::make('發票和付款方式')
                    ->icon('heroicon-o-credit-card')
                    ->schema([
                        Forms\Components\Select::make('area')
                            ->label('所屬區域')
                            ->options(EnumArea::getOptions())
                            ->helperText('下拉選單目前是假資料，等提供實際清單後會更新')
                            ->disabled(),
                        Forms\Components\Select::make('invoice_issue_method')
                            ->label('發票開立方式')
                            ->options(EnumInvoiceIssueMethod::getOptions())
                            ->helperText('下拉選單目前是假資料，等提供實際清單後會更新')
                            ->disabled(),
                        Forms\Components\Select::make('invoice_tax_type')
                            ->label('發票開立稅別')
                            ->options(EnumInvoiceTaxType::getOptions())
                            ->helperText('下拉選單目前是假資料，等提供實際清單後會更新')
                            ->disabled(),
                        Forms\Components\TextInput::make('carrier_number')
                            ->label('載具號碼')
                            ->maxLength(50)
                            ->placeholder('請輸入載具號碼')
                            ->disabled(),


                    ])
                    ->columns(2),


                self::getFormTimestamps(),
                self::getFormSectionAdminId(),

            ]);
    }

    public static function table(Table $table): Table
    {
        // Get the parent table schema
        $parentTable = BaseClientResource::table($table);

        return $table
            ->columns([
                ...$parentTable->getColumns(true),


                Tables\Columns\TextColumn::make('sale_code')
                    ->label('業務員代號')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('web_account')
                    ->label('網路登錄帳號')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('price_type')
                    ->label('售價類型')
                    ->formatStateUsing(fn(string $state): string => EnumPriceType::tryFrom($state)?->getLabel() ?? $state)
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('area')
                    ->label('所屬區域')
                    ->formatStateUsing(fn(int $state): string => EnumArea::tryFrom($state)?->getLabel() ?? (string)$state)
                    ->toggleable(),


                Tables\Columns\IconColumn::make('email_statement')
                    ->label('EMail對帳單')
                    ->boolean()
                    ->toggleable(),

            ]);
    }

    public static function getRelations(): array
    {
        return [
            ...parent::getRelations(),
            SpecialPriceRelationManager::class,
        ];
    }


    public static function getPages(): array
    {
        return [
            'index' => ClientResource\Pages\ListClients::route('/'),
            'create' => ClientResource\Pages\CreateClient::route('/create'),
            'edit' => ClientResource\Pages\EditClient::route('/{record}/edit'),
            'view' => ClientResource\Pages\ViewClient::route('/{record}'),
        ];
    }
}
