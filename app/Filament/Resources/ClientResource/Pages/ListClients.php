<?php

namespace App\Filament\Resources\ClientResource\Pages;

use App\Filament\Resources\ClientResource;
use App\StateMachines\ListClientsStateMachine;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\ExportAction;

class ListClients extends ListRecords
{
    protected static string $resource = ClientResource::class;

    protected static string $view = 'filament.resources.client-resource.pages.list-clients';

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function getViewData(): array
    {
        $graphData = ListClientsStateMachine::generateMermaidFlowchart();
        return compact('graphData');
    }
}
