<?php

namespace App\Filament\Resources\ClientResource\Pages;

use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use App\Filament\Resources\ClientResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\HtmlString;

class EditClient extends EditRecord
{
    use CCTraitRedirectToIndex;
    protected static string $resource = ClientResource::class;

    public function getSubheading(): ?HtmlString
    {
        return new HtmlString('<span style="color: red;">由『前台』或是『ERP系統』來更新的資料，會關閉編輯功能。。</span>');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
