<?php

namespace App\Filament\Resources\ClientResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\DetachBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;

class SpecialPriceRelationManager extends RelationManager
{
    use CCTraitColumn;
    use CCTraitAction;


    protected static string $relationship = 'specialProductSpecifications';

    protected static ?string $recordTitleAttribute = 'sku';

    protected static ?string $label = '專屬價格';

    protected static ?string $modelLabel = '專屬價格';

    protected static ?string $title = '專屬價格';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('special_price')
                    ->label('專屬價格')
                    ->numeric()
                    ->minValue(0)
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('sku')
            ->defaultSort('product_specifications.id', 'desc')
            ->columns([

                self::getColumnTextCopyable('sku')
                    ->label('產品規格型號')
                    ->searchable(),

                self::getColumnTranslation('title')
                    ->label('產品規格名稱'),



                Tables\Columns\TextInputColumn::make('pivot.special_price')
                    ->label('專屬價格')
                    ->rules(['required', 'numeric', 'min:0'])
                    ->updateStateUsing(function($state, $record){
                        $record->pivot->special_price = $state;
                        $record->pivot->save();
                    }),

                TextColumn::make('listing_price')
                    ->label('建議售價'),

                TextColumn::make('selling_price')
                    ->label('銷售價一')
                    ->abbr('B2B 官網的銷售價四', asTooltip: true),

                TextColumn::make('selling_price2')
                    ->label('銷售價二')
                    ->abbr('B2B 官網的銷售價二', asTooltip: true),

                TextColumn::make('selling_price3')
                    ->label('銷售價三')
                    ->abbr('B2B 官網的銷售價三', asTooltip: true),

                TextColumn::make('selling_price4')
                    ->label('銷售價四')
                    ->abbr('B2C 官網的銷售價四', asTooltip: true),

            ])
            ->filters([
                //
            ])
            ->headerActions([

                Tables\Actions\Action::make('bulk_attach')
                    ->label('新增產品規格')
                    ->icon('heroicon-o-plus')
                    ->modalWidth('7xl')
                    ->modalHeading('新增產品規格')
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('關閉')
                    ->modalContent(fn() => view('livewire.product-specifications.special-price-selector', [
                        'record' => $this->getOwnerRecord(),
                    ]))
                    ->after(function ($livewire) {
                        $livewire->refreshRecords();
                    })
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionDetach(),
            ])
            ->bulkActions([
                DetachBulkAction::make()
                    ->label('批量移除')
                    ->modalHeading('批量移除專屬價格')
                    ->modalSubmitActionLabel('確認移除')
                    ->modalCancelActionLabel('取消'),
            ]);
    }
}
