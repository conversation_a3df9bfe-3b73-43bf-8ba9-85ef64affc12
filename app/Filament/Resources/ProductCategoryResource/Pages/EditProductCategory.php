<?php

namespace App\Filament\Resources\ProductCategoryResource\Pages;

use Illuminate\Support\HtmlString;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource\Pages\EditProductCategory as BaseEditProductCategory;

class EditProductCategory extends BaseEditProductCategory
{
    public function getSubheading(): ?HtmlString
    {
        return new HtmlString('<span style="color: red;">由『前台』或是『ERP系統』來更新的資料，會關閉編輯功能。。</span>');
    }

}
