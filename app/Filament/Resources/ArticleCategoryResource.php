<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ArticleCategoryResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class ArticleCategoryResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitAction;

    protected static ?string $model = ArticleCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-inbox';

    public static function getNavigationGroup(): ?string
    {
        return '作品集管理';
    }

    /**
     * @return string|null
     */
    public static function getLabel(): ?string
    {
        return '作品集類別';
    }

//    public static function getNavigationBadge(): ?string
//    {
//        $count = CountService::getArticleCategoryCount();
//        $unit = CountService::getUnit('article_category');
//
//        return "$count $unit";
//    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        self::getFormToggle('status')
                            ->label('啟用/停用'),
                        self::getFormKey()
                            ->reactive()
                            ->debounce(500)
                            ->afterStateUpdated(function ($state, callable $set)
                            {
                                $set('slug', Str::slug($state));
                            }),
                        self::getFormSort(),
                        self::getFormSlug(),
                    ]),

                self::getFormSectionImage(),

                self::getTabLanguage([
                    self::getFormTitle(),
                    self::getFormEditor('content')
                        ->label('內容 - 圖文編輯器')
                        ->placeholder('請輸入內容')
                        ->columnSpan('full'),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTextKey(),
                self::getColumnTranslation('title', true)
                    ->label('作品集類別標題'),
                self::getColumnTextStatus(),
                self::getColumnImage('image','圖片'),
                self::getColumnTextCount()->label('作品集總數'),
                self::getColumnTextInputSort(),
                self::getColumnTextSlug(),

            ])
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //ArticleCategoryResource\RelationManagers\TranslationsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListArticleCategories::route('/'),
            'create' => Pages\CreateArticleCategory::route('/create'),
            'edit'   => Pages\EditArticleCategory::route('/{record}/edit'),
            'view'   => Pages\ViewArticleCategory::route('/{record}'),
        ];
    }


    public static function canAccess(): bool
    {
        return config('cs.article_visible') && Gate::allows('view-any', ArticleCategory::class);
    }


}
