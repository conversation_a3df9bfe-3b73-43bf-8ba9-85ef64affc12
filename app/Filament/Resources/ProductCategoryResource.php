<?php

namespace App\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource as BaseProductCategoryResource;
class ProductCategoryResource extends BaseProductCategoryResource
{
    public static function getPages(): array
    {
        return [
            'index' => ProductCategoryResource\Pages\ListProductCategories::route('/'),
            'create' => ProductCategoryResource\Pages\CreateProductCategory::route('/create'),
            'edit' => ProductCategoryResource\Pages\EditProductCategory::route('/{record}/edit'),
            'view' => ProductCategoryResource\Pages\ViewProductCategory::route('/{record}'),
        ];
    }

}
