<?php

namespace App\Filament\Resources\ProductResource\RelationManagers;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\RelationManagers\SpecificationsRelationManager as BaseProductSpecificationsRelationManager;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ProductSpecificationsRelationManager extends BaseProductSpecificationsRelationManager
{
    protected static string $relationship = 'specifications';

    public function form(Form $form): Form
    {
        $parentForm = parent::form($form);
        $parentComponents = array_filter($parentForm->getComponents(), function ($component) {
            return $component->getId() != 'data_settings';
        });

        return $parentForm->schema([
            ...$parentComponents,

            Section::make('b2c_data_settings')
                ->id('b2c_data_settings')
                ->heading('B2C 數據設定')
                ->schema([

                    TextInput::make('listing_price')
                        ->label('建議售價')
                        ->numeric() // 確保是數字
                        ->minValue(0)
                        ->required()
                        ->helperText('建議售價應大於或等於0'),


                    TextInput::make('selling_price')
                        ->label('售價一')
                        ->numeric() // 確保是數字
                        ->minValue(0)
                        ->required()
                        ->helperText('售價一應大於或等於0'),

                    TextInput::make('inventory')
                        ->label('庫存')
                        ->numeric() // 確保是數字
                        ->minValue(0)
                        ->default(0)
                        ->required()
                        ->helperText('庫存應大於或等於0'),

                ]),

            Section::make('b2c_data_settings')
                ->id('b2b_data_settings')
                ->heading('B2B 數據設定')
                ->schema([

                    TextInput::make('selling_price2')
                        ->label('售價二')
                        ->numeric() // 確保是數字
                        ->minValue(0)
                        ->required(),

                    TextInput::make('selling_price3')
                        ->label('售價三')
                        ->numeric() // 確保是數字
                        ->minValue(0)
                        ->required(),

                    TextInput::make('selling_price4')
                        ->label('售價四')
                        ->numeric() // 確保是數字
                        ->minValue(0)
                        ->required(),

                    TextInput::make('selling_price5')
                        ->label('售價五')
                        ->numeric() // 確保是數字
                        ->minValue(0)
                        ->required(),

                ]),

        ]);
    }

    public function table(Table $table): Table
    {
        $parentTable = parent::table($table);

        return $parentTable
            ->columns([
                ... $parentTable->getColumns(),

                TextColumn::make('selling_price2')
                    ->label('銷售價二')
                    ->abbr('B2B 官網的銷售價二', asTooltip: true),

                TextColumn::make('selling_price3')
                    ->label('銷售價三')
                    ->abbr('B2B 官網的銷售價三', asTooltip: true),

                TextColumn::make('selling_price4')
                    ->label('銷售價四')
                    ->abbr('B2B 官網的銷售價四', asTooltip: true),

                TextColumn::make('selling_price5')
                    ->label('銷售價五')
                    ->abbr('B2B 官網的銷售價五', asTooltip: true),
            ]);
    }
}
