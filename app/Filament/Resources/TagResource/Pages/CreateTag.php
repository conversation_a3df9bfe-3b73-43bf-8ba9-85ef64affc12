<?php

namespace App\Filament\Resources\TagResource\Pages;

use App\Filament\Resources\TagResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateTag extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = TagResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
