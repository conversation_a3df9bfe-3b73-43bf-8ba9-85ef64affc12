<?php

namespace App\Filament\Resources\TagResource\Pages;

use App\Filament\Resources\TagResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumTagType;

class ListTags extends ListRecords
{
    protected static string $resource = TagResource::class;

    public function getTabs(): array
    {
        if (config('cs.share_tag')) {
            return [];
        }

        $cases = collect(EnumTagType::cases())
            ->filter(function ($case)
            {
                if ($case->value === EnumTagType::SHARED->value) {
                    return false;
                }
                if ($case->value === EnumTagType::ARTICLE->value) {
                    return config('cs.article_visible');
                }
                if ($case->value === EnumTagType::PRODUCT->value) {
                    return config('cs.product_visible');
                }
                return true;
            });

        $cases = $cases->mapWithKeys(function ($type)
        {
            return [
                $type->value => Tab::make($type->value)
                    ->label($type->getLabel())
                    ->query(fn($query) => $query->where('type', $type->value)),
            ];
        })->toArray();

        if (count($cases) < 2) {
            return [];
        }

        return $cases;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
