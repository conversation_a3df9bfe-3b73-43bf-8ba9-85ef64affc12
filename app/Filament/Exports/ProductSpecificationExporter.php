<?php

namespace App\Filament\Exports;

use Stephenchenorg\BaseFilamentPlugin\Filament\Exports\ProductSpecificationExporter as BaseProductSpecificationExporter;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Filament\Actions\Exports\ExportColumn;

class ProductSpecificationExporter extends BaseProductSpecificationExporter
{
    protected static ?string $model = ProductSpecification::class;

    public static function getColumns(): array
    {
        $parentColumns = parent::getColumns();

        return [
            ...$parentColumns,
            ExportColumn::make('selling_price2')
                ->label('銷售價格二'),
            ExportColumn::make('selling_price3')
                ->label('銷售價格三'),
            ExportColumn::make('selling_price4')
                ->label('銷售價格四'),
            ExportColumn::make('selling_price5')
                ->label('銷售價格五'),
        ];
    }

}
