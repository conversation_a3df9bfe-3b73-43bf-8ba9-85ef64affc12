<?php

namespace App\Filament\Pages;

use Exception;
use Filament\Actions\Action;
use Filament\Actions\Concerns\CanSubmitForm;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\CanUseDatabaseTransactions;
use Filament\Pages\Page;
use App\Services\ErpService;
use App\Jobs\UpdateProductsJob;
use Illuminate\Support\Facades\Cache;
use App\StateMachines\ErpFunctionStateMachine;


/**
 * @property Form $form
 */
class ErpFunctionPage extends Page implements HasForms
{
    use CanSubmitForm;
    use InteractsWithForms;
    use CanUseDatabaseTransactions;


    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $view = 'filament.pages.erp-function-page';

    protected static ?string $navigationGroup = 'ERP';
    protected static ?string $navigationLabel = '同步 -  ERP產品資料';
    protected static ?string $title = '同步 -  ERP產品資料';

    protected ErpService $erpService;

    /**
     * @var array|null
     */
    public ?array $data = [];

    public function __construct()
    {
        $this->erpService = new ErpService();
    }

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return true;
    }

    public function mount(): void
    {
    }

    /**
     * @param Form $form
     * @return Form
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema($this->getFormSchema())
            ->statePath('data');
    }


    protected function getFormSchema(): array
    {
        return [
            \Filament\Forms\Components\TextInput::make('product_codes')
                ->label('產品代號')
                ->helperText('請輸入產品代號，多個代號請用逗號分隔，如果不輸入將會更新全部產品'),
        ];
    }

    protected function getActions(): array
    {
        return [
            Action::make('update_products')
                ->label('更新產品')
                ->action(fn() => $this->update()),
        ];
    }

    /**
     * @return void
     */
    public function update(): void
    {
        $data = $this->form->getState();
        $productCodes = $data['product_codes'];
        if(!empty($data['product_codes'])){
            $this->updateProducts($productCodes);
        }else{
            $this->updateProductsByJob();
        }

    }

    public function updateProducts(string $productCodes): void
    {
        try {
            $productCodes = array_map('trim', explode(',', $productCodes));
            $this->erpService->updateProducts($productCodes);

            Notification::make()
                ->success()
                ->title('產品更新成功')
                ->send();

        } catch (Exception $e) {
            Notification::make()
                ->danger()
                ->title('產品更新失敗')
                ->body($e->getMessage())
                ->send();
        }
    }

    public function updateProductsByJob(): void
    {
        try {
            $job = new UpdateProductsJob();
            $job->dispatch($job);

            Notification::make()
                ->success()
                ->title('已開始更新產品')
                ->body('產品更新已加入佇列中，請稍後查看結果')
                ->send();

            // 定期檢查工作狀態
            $this->dispatch('check-job-status', jobId: $job->jobId);

        } catch (Exception $e) {
            Notification::make()
                ->danger()
                ->title('無法啟動產品更新')
                ->body($e->getMessage())
                ->send();
        }
    }

    public function checkJobStatus(string $jobId): void
    {
        $status = Cache::get($jobId);

        if ($status === 'completed') {
            Notification::make()
                ->success()
                ->title('產品更新完成')
                ->send();
            return;
        }

        if (str_starts_with($status, 'failed')) {
            Notification::make()
                ->danger()
                ->title('產品更新失敗')
                ->body($status)
                ->send();
            return;
        }

        // 如果還在運行中，繼續檢查
        if ($status === 'running') {
            $this->dispatch('check-job-status', jobId: $jobId)->delay(5);
        }
    }

    public function getViewData(): array
    {
        $graphData = ErpFunctionStateMachine::generateMermaidFlowchart();
        return compact('graphData');
    }
}
