<?php

namespace App\Filament\Imports;

use Stephenchenorg\BaseFilamentPlugin\Filament\Imports\ProductSpecificationImporter as BaseProductSpecificationImporter;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Filament\Actions\Imports\ImportColumn;

class ProductSpecificationImporter extends BaseProductSpecificationImporter
{
    protected static ?string $model = ProductSpecification::class;

    public static function getColumns(): array
    {
        $parentColumns = parent::getColumns();

        return [
            ...$parentColumns,
            ImportColumn::make('selling_price2')
                ->label('銷售價二')
                ->exampleHeader('銷售價二')
                ->example([900, 1800])
                ->rules(['required', 'numeric', 'min:0'])
                ->requiredMapping(),

            ImportColumn::make('selling_price3')
                ->label('銷售價三')
                ->exampleHeader('銷售價三')
                ->example([900, 1800])
                ->rules(['required', 'numeric', 'min:0'])
                ->requiredMapping(),

            ImportColumn::make('selling_price4')
                ->label('銷售價四')
                ->exampleHeader('銷售價四')
                ->example([900, 1800])
                ->rules(['required', 'numeric', 'min:0'])
                ->requiredMapping(),


            ImportColumn::make('selling_price5')
                ->label('銷售價五')
                ->exampleHeader('銷售價五')
                ->example([900, 1800])
                ->rules(['required', 'numeric', 'min:0'])
                ->requiredMapping(),
        ];
    }

}
