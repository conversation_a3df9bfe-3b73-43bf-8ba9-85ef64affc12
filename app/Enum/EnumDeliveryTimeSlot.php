<?php

namespace App\Enum;

enum EnumDeliveryTimeSlot: int
{
    case ANY_TIME = 1;
    case MORNING = 2;
    case AFTERNOON = 3;
    case EVENING = 4;

    public function getLabel(): string
    {
        return match($this) {
            self::ANY_TIME => '不指定',
            self::MORNING => '上午 (9:00-12:00)',
            self::AFTERNOON => '下午 (12:00-17:00)',
            self::EVENING => '晚上 (17:00-20:00)',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::ANY_TIME->value => self::ANY_TIME->getLabel(),
            self::MORNING->value => self::MORNING->getLabel(),
            self::AFTERNOON->value => self::AFTERNOON->getLabel(),
            self::EVENING->value => self::EVENING->getLabel(),
        ];
    }
}
