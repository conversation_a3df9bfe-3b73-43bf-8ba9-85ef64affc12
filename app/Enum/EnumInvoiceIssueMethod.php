<?php

namespace App\Enum;

enum EnumInvoiceIssueMethod: int
{
    case PAPER = 1;
    case ELECTRONIC = 2;
    case CLOUD = 3;

    public function getLabel(): string
    {
        return match($this) {
            self::PAPER => '紙本發票',
            self::ELECTRONIC => '電子發票',
            self::CLOUD => '雲端發票',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::PAPER->value => self::PAPER->getLabel(),
            self::ELECTRONIC->value => self::ELECTRONIC->getLabel(),
            self::CLOUD->value => self::CLOUD->getLabel(),
        ];
    }
}
