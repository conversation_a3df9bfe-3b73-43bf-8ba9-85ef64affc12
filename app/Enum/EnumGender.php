<?php

namespace App\Enum;

enum EnumGender: int
{
    case UNSPECIFIED = 0;
    case MALE = 1;
    case FEMALE = 2;

    public function getLabel(): string
    {
        return match($this) {
            self::UNSPECIFIED => '未指定',
            self::MALE => '男',
            self::FEMALE => '女',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::UNSPECIFIED->value => self::UNSPECIFIED->getLabel(),
            self::MALE->value => self::MALE->getLabel(),
            self::FEMALE->value => self::FEMALE->getLabel(),
        ];
    }
}
