<?php

namespace App\Enum;

use Filament\Support\Contracts\HasLabel;

enum EnumCustomerLevel: int implements HasLabel
{
    case NONE = 0;     // 不區分
    case A = 1;        // A級
    case B = 2;        // B級
    case C = 3;        // C級
    case D = 4;        // D級
    case E = 5;        // E級

    public function getLabel(): string
    {
        return match($this) {
            self::NONE => '不區分',
            self::A => 'A級',
            self::B => 'B級',
            self::C => 'C級',
            self::D => 'D級',
            self::E => 'E級',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::NONE->value => self::NONE->getLabel(),
            self::A->value => self::A->getLabel(),
            self::B->value => self::B->getLabel(),
            self::C->value => self::C->getLabel(),
            self::D->value => self::D->getLabel(),
            self::E->value => self::E->getLabel(),
        ];
    }
}
