<?php

namespace App\Enum;

enum EnumPriceType: string
{
    case LISTING_PRICE = 'listing_price';
    case PRICE_1 = 'selling_price';
    case PRICE_2 = 'selling_price2';
    case PRICE_3 = 'selling_price3';
    case PRICE_4 = 'selling_price4';

    public function getLabel(): string
    {
        return match($this) {
            self::LISTING_PRICE => '建議售價',
            self::PRICE_1 => '銷售價一',
            self::PRICE_2 => '銷售價二',
            self::PRICE_3 => '銷售價三',
            self::PRICE_4 => '銷售價四',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::PRICE_1->value => self::PRICE_1->getLabel(),
            self::PRICE_2->value => self::PRICE_2->getLabel(),
            self::PRICE_3->value => self::PRICE_3->get<PERSON>abel(),
            self::PRICE_4->value => self::PRICE_4->getLabel(),
        ];
    }

    public static function fromNumberToValue(int $number): ?string
    {
        return match ($number) {
            0 => self::PRICE_1->value,
            1 => self::PRICE_2->value,
            2 => self::PRICE_3->value,
            3 => self::PRICE_4->value,
            4 => self::LISTING_PRICE->value,
            default => null,
        };
    }
}
