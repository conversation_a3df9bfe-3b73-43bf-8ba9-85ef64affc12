<?php

namespace App\Enum;

enum EnumInvoiceTaxType: int
{
    case TAXABLE = 1;
    case TAX_FREE = 2;
    case ZERO_TAX = 3;

    public function getLabel(): string
    {
        return match($this) {
            self::TAXABLE => '應稅',
            self::TAX_FREE => '免稅',
            self::ZERO_TAX => '零稅率',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::TAXABLE->value => self::TAXABLE->getLabel(),
            self::TAX_FREE->value => self::TAX_FREE->getLabel(),
            self::ZERO_TAX->value => self::ZERO_TAX->getLabel(),
        ];
    }
}
