<?php

namespace App\Enum;

enum EnumArea: int
{
    case NORTH = 1;
    case CENTRAL = 2;
    case SOUTH = 3;
    case EAST = 4;
    case ISLANDS = 5;

    public function getLabel(): string
    {
        return match($this) {
            self::NORTH => '北部',
            self::CENTRAL => '中部',
            self::SOUTH => '南部',
            self::EAST => '東部',
            self::ISLANDS => '離島',
        };
    }

    public static function getOptions(): array
    {
        return [
            self::NORTH->value => self::NORTH->getLabel(),
            self::CENTRAL->value => self::CENTRAL->getLabel(),
            self::SOUTH->value => self::SOUTH->getLabel(),
            self::EAST->value => self::EAST->getLabel(),
            self::ISLANDS->value => self::ISLANDS->getLabel(),
        ];
    }
}
