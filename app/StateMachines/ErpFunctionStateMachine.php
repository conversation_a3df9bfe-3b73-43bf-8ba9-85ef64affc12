<?php

namespace App\StateMachines;

class ErpFunctionStateMachine
{
    public static function generateMermaidFlowchart(): string
    {
        return <<<EOT
graph LR
    A[開始] --> B[產品更新頁面]
    B --> C[輸入產品代號]
    C --> D[按下更新按鈕]

    D --> E[呼叫ERP API]

    E --> G{資料取得成功?}
    G -->|是| H[更新後台資料]
    G -->|否| I[顯示錯誤訊息]
    I --> C

    H --> J{更新成功?}
    J -->|是| K[顯示成功訊息]
    J -->|否| L[顯示失敗訊息]
    L --> C

    K --> M[完成]
EOT;
    }
}
