<?php

namespace App\GraphQL\Types;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductSpecificationType as BaseProductSpecificationType;
use GraphQL\Type\Definition\Type;
use App\Services\ServiceProduct;

class ProductSpecificationType extends BaseProductSpecificationType
{

    public function fields(): array
    {
        $fields = [
            ...parent::fields(),
            'listing_price'   => [
                'type'        => Type::float(),
                'description' => 'The listing price of the product',
                'resolve'   => function ($root,$args) {
                    return ServiceProduct::getSpecListingPrice($root);
                },
            ],
            'selling_price'   => [
                'type'        => Type::float(),
                'description' => 'The selling price of the product specification',
                'resolve'   => function ($root,$args) {
                    return ServiceProduct::getSpecSellingPrice($root);
                },
            ],
        ];

        return $fields;
    }
}
