<?php

namespace App\GraphQL\Types;

use GraphQL\Type\Definition\Type;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Auth\CustomerType as BaseCustomerType;

class CustomerType extends BaseCustomerType
{

    public function fields(): array
    {
        $fields = [
            ...parent::fields(),
            'code' => [
                'type'        => Type::string(),
                'description' => 'The code of the client',
            ],
            'id_number' => [
                'type' => Type::string(),
                'description' => '身份證號',
            ],
            'birthday' => [
                'type' => Type::string(),
                'description' => '出生日期',
            ],
            'gender' => [
                'type' => Type::int(),
                'description' => '性別',
            ],
            'account' => [
                'type' => Type::string(),
                'description' => '客戶帳號',
                'resolve' => function ($root) {
                    return $root->email;
                },
            ],
            'phone1' => [
                'type'        => Type::string(),
                'description' => 'The primary phone number of the client',
            ],
            'phone2' => [
                'type'        => Type::string(),
                'description' => 'The secondary phone number of the client',
            ],
            'mobile' => [
                'type'        => Type::string(),
                'description' => 'The mobile number of the client',
            ],
            'address' => [
                'type'        => Type::string(),
                'description' => 'The address of the client',
            ],
            'invoice_address' => [
                'type'        => Type::string(),
                'description' => 'The invoice address of the client',
            ],
        ];

        return $fields;
    }
}
