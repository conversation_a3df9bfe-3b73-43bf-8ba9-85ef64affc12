<?php

namespace App\GraphQL\Types\Order;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\Models\OrderItem;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class OrderItemType extends GraphQLType
{
    protected $attributes = [
        'name'        => EnumNames::OrderItem,
        'description' => 'A type that represents an order item',
        'model'       => OrderItem::class,
    ];

    public function fields(): array
    {
        return [
            'id' => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the order item',
            ],
            'order_id' => [
                'type'        => Type::int(),
                'description' => 'The order ID',
            ],
            'product_specification_id' => [
                'type'        => Type::int(),
                'description' => 'The product specification ID',
            ],
            'title' => [
                'type'        => Type::string(),
                'description' => 'The title of the product specification',
            ],
            'quantity' => [
                'type'        => Type::int(),
                'description' => 'The quantity',
            ],
            'unit_price' => [
                'type'        => Type::float(),
                'description' => 'The unit price',
            ],
            'total_amount' => [
                'type'        => Type::float(),
                'description' => 'The total amount for this item',
            ],
            'productSpecification' => [
                'type'        => GraphQL::type(EnumNames::ProductSpecification),
                'description' => 'The product specification',
            ],
        ];
    }
}
