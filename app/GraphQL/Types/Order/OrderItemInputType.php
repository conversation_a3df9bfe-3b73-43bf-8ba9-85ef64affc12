<?php

namespace App\GraphQL\Types\Order;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\InputType;

class OrderItemInputType extends InputType
{
    protected $attributes = [
        'name' => 'OrderItemInput',
        'description' => '訂單項目輸入',
    ];

    public function fields(): array
    {
        return [
            'product_specification_id' => [
                'name' => 'product_specification_id',
                'type' => Type::nonNull(Type::int()),
                'rules' => ['required', 'exists:product_specifications,id'],
            ],
            'quantity' => [
                'name' => 'quantity',
                'type' => Type::nonNull(Type::int()),
                'rules' => ['required', 'integer', 'min:1'],
            ],
        ];
    }
}
