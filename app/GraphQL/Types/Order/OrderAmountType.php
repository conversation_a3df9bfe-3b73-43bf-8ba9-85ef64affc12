<?php

namespace App\GraphQL\Types\Order;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class OrderAmountType extends GraphQLType
{
    protected $attributes = [
        'name' => EnumNames::OrderAmount,
        'description' => 'Result of cart calculation with amounts and costs',
    ];

    public function fields(): array
    {
        return [
            'item_amount' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'Total amount of all items',
            ],
            'shipping_cost' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'Shipping cost',
            ],
            'tax' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'Tax amount',
            ],
            'total_amount_taxed' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'Total amount including tax',
            ],
            'total_amount_untaxed' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'Total amount excluding tax',
            ],
        ];
    }
}
