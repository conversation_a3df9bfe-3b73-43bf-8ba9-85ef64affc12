<?php

namespace App\GraphQL\Queries;

use App\Services\ServiceOrder;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class ValidateCartQuery extends Query
{

    protected $attributes = [
        'name' => EnumNames::ValidateCart,
        'description' => 'Validates cart items against inventory and returns validation result',
    ];

    /**
     * @var ServiceOrder
     */
    private ServiceOrder $serviceOrder;

    /**
     * @param ServiceOrder $serviceOrder
     */
    public function __construct(ServiceOrder $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::CartValidationResult);
    }

    public function args(): array
    {
        return [
            'items' => [
                'name' => 'items',
                'type' => Type::nonNull(Type::listOf(GraphQL::type('OrderItemInput'))),
                'rules' => ['required', 'array', 'min:1', 'max:15'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {

        $this->serviceOrder->checkAndLockInventory($args['items']);

        return [
            'success' => true,
        ];
    }
}
