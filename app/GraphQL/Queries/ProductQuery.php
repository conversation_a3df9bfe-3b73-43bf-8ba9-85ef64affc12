<?php

namespace App\GraphQL\Queries;

use App\Models\Product;
use App\Services\ServiceProduct;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product\ProductQuery as BaseProductQuery;

class ProductQuery extends BaseProductQuery
{
    public function resolve($root, $args)
    {
        $query = Product::query();

        $query = self::filterEnabledQuery($query,'category',true);

        $query = $this->translateQuery($query, [
            'translations', 'category.translations', 'tags.translations', 'attributes.translations',
            'attributes.items.translations',
        ]);

        $query = ServiceProduct::eagerLoadSpecialPriceFromProduct($query);

        $query = $this->applyFieldFilter($query, $args, ['id','part_number']);

        return $query->firstOrFail();
    }
}
