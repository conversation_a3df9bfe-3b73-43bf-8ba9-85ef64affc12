<?php

namespace App\GraphQL\Queries;

use App\Models\ProductSpecification;
use App\Services\ServiceProduct;
use GraphQL\Type\Definition\Type;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product\ProductSpecificationsQuery as BaseProductSpecificationsQuery;

class ProductSpecificationsQuery extends BaseProductSpecificationsQuery
{
    public function args(): array
    {
        return [
            ...parent::args(),
            'ids' => [
                'name' => 'ids',
                'type' => Type::listOf(Type::int()),
                'rules' => ['nullable'],
            ],
        ];
    }

    public function resolve($root, $args): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = ProductSpecification::query();

        if(isset($args['ids'])) {
            $query->whereIn('id', $args['ids']);
        }

        if (isset($args['product_id'])) {
            $query->where('product_id', $args['product_id']);
        }

        $query = $this->filterEnabledQuery($query);

        $query = $this->translateQuery($query, ['translations', 'product.translations']);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        $query = ServiceProduct::eagerLoadSpecialPriceFromSpec($query);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
