<?php

namespace App\GraphQL\Queries;

use App\Models\Product;
use App\Services\ServiceProduct;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product\ProductsQuery as BaseProductsQuery;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;


class ProductsQuery extends BaseProductsQuery
{
    public function resolve($root, $args): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = Product::query();


        if (isset($args['category_id'])) {
            $categoryIds = ProductCategory::query()->find($args['category_id'])->descendantsWithSelf()->pluck('id')->toArray();
            $query->whereIn('product_category_id', $categoryIds);
        }

        if (isset($args['type'])) {
            $query->where('type', $args['type']);
        }

        if (isset($args['is_hottest'])) {
            $query = $this->filterHottestQuery($query, $args['is_hottest']);
        }

        if (isset($args['is_newest'])) {
            $query = $this->filterNewestQuery($query, $args['is_newest']);
        }

        if (isset($args['search'])) {
            $search = $args['search'];
            $query->where(function ($query) use ($search) {
                return $query
                    ->whereHas('translations', function ($q) use ($search) {
                        $q->where('title', 'like', '%' . $search . '%');
                    })
                    ->orWhere('part_number', 'like', '%' . $search . '%');
            });
        }


        if (isset($args['price_from'])) {
            $query->whereHas('specifications', function ($subQuery) use ($args) {
                $subQuery->select('product_id')
                    ->groupBy('product_id')
                    ->havingRaw('MIN(listing_price) >= ?', [$args['price_from']]); // 直接使用聚合函數
            });
        }

        if (isset($args['price_to'])) {
            $query->whereHas('specifications', function ($subQuery) use ($args) {
                $subQuery->select('product_id')
                    ->groupBy('product_id')
                    ->havingRaw('MIN(listing_price) <= ?', [$args['price_to']]);
            });
        }

        if (isset($args['union_tags']) && is_array($args['union_tags'])) {
            $query->whereHas('tags', function ($q) use ($args) {
                $q->whereIn('tags.id', $args['union_tags']);
            });
        }

        if (isset($args['intersect_tags']) && is_array($args['intersect_tags'])) {
            $query->whereHas('tags', function ($q) use ($args) {
                $q->whereIn('tags.id', $args['intersect_tags']);
            }, '=', count($args['intersect_tags']));
        }


        $query = $this->filterCreatedTimeQuery($query, $args['started_at'] ?? null, $args['ended_at'] ?? null);

        $query = $this->filterTimeIntervalQuery($query);

        $query = $this->filterEnabledQuery($query,'category',true);

        $query = $this->translateQuery($query, [
            'translations', 'category.translations', 'tags.translations', 'attributes.translations',
            'attributes.items.translations',
        ]);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        $query = ServiceProduct::eagerLoadSpecialPriceFromProduct($query);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
