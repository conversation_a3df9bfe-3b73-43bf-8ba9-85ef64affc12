<?php

namespace App\GraphQL\Queries;

use App\Services\ServiceOrder;
use Exception;
use GraphQL\Type\Definition\Type;
use Illuminate\Validation\Rule;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class CalculateCartQuery extends Query
{
    protected $attributes = [
        'name' => EnumNames::CalculateCart,
        'description' => 'Calculate cart amounts including tax and shipping',
    ];

    /**
     * @var ServiceOrder
     */
    private ServiceOrder $serviceOrder;

    /**
     * @param ServiceOrder $serviceOrder
     */
    public function __construct(ServiceOrder $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::OrderAmount);
    }

    public function args(): array
    {
        return [

            // 購物車項目
            'items' => [
                'name' => 'items',
                'type' => Type::nonNull(Type::listOf(GraphQL::type('OrderItemInput'))),
                'rules' => ['required', 'array', 'min:1', 'max:15'],
            ],

            // 發票方式 (必填)
            'invoice_method' => [
                'name' => 'invoice_method',
                'type' => Type::nonNull(Type::string()),
                'rules' => [
                    'required',
                    Rule::in(EnumInvoiceMethod::getAvailableMethodValues())
                ],
            ],

            // 運送方式 (必填)
            'shipping_method' => [
                'name' => 'shipping_method',
                'type' => Type::nonNull(Type::string()),
                'rules' => [
                    'required',
                    Rule::in(EnumShippingMethod::getAvailableMethodValues())
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        $amounts = $this->serviceOrder->calculateOrderAmounts(
            $args['items'],
            $args['shipping_method'],
            $args['invoice_method']
        );

        return [
            'item_amount' => $amounts['item_amount'],
            'tax' => $amounts['tax'],
            'total_amount_untaxed' => $amounts['total_amount_untaxed'],
            'total_amount_taxed' => $amounts['total_amount_taxed'],
            'shipping_cost' => $amounts['shipping_cost'],
        ];
    }
}
