<?php

namespace App\GraphQL\Mutations\Customer;

use App\Services\Auth\ServiceCustomerRegister;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerRegisterMutation as BaseCustomerRegisterMutation;

class CustomerRegisterMutation extends BaseCustomerRegisterMutation
{
    public function resolve($root, $args): string
    {
        $service = new ServiceCustomerRegister();
        $service->register($args);
        return __('auth.registration_success');
    }
}
