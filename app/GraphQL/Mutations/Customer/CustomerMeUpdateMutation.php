<?php

namespace App\GraphQL\Mutations\Customer;

use App\Models\Customer;
use App\Services\ErpAuthService;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Mutation;
use <PERSON>chenorg\BaseFilamentPlugin\GraphQL\Base\BaseCustomerJwtMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use <PERSON>chenorg\CsCoreFilamentPlugin\CCUtility;

class CustomerMeUpdateMutation extends BaseCustomerJwtMutation
{
    protected $attributes = [
        'name' => EnumNames::CustomerMeUpdate,
        'description' => 'Update customer profile',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [
            'name' => [
                'name' => 'name',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:50'],
            ],
            'email' => [
                'name' => 'email',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:70', 'email'],
            ],
            'id_number' => [
                'name' => 'id_number',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:10', 'regex:/^[A-Z][12]\d{8}$/'],
            ],
            'birthday' => [
                'name' => 'birthday',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required','date'],
            ],
            'gender' => [
                'name' => 'gender',
                'type' => Type::nonNull(Type::int()),
                'rules' => ['required','numeric', 'max:255', 'in:0,1,2'],
            ],
            'phone1' => [
                'name' => 'phone1',
                'type' => Type::string(),
                'rules' =>[
                    'nullable',
                    'max:10',
                    function ($attribute, $value, $fail)
                    {
                        if (!CCUtility::validatePhoneDomestic($value)) {
                            $fail('請符合09開頭電話號碼。');
                        }
                    },
                ],
            ],
            'phone2' => [
                'name' => 'phone2',
                'type' => Type::string(),
                'rules' =>[
                    'nullable',
                    'max:10',
                    function ($attribute, $value, $fail)
                    {
                        if (!CCUtility::validatePhoneDomestic($value)) {
                            $fail('請符合09開頭電話號碼。');
                        }
                    },
                ],
            ],
            'mobile' => [
                'name' => 'mobile',
                'type' => Type::string(),
                'rules' => ['nullable', 'max:20'],
            ],
            'address' => [
                'name' => 'address',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:255'],
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $customer = $this->service->update($args);
        $erpAuthService = new ErpAuthService();
        $erpAuthService->updateToErp($customer);
        return '更新成功';

    }
}
