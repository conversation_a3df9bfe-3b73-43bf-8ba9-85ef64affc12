<?php

namespace App\GraphQL\Mutations\Client;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use App\Services\ServiceClient;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Mutation;

class ClientLoginMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::ClientLogin,
        'description' => 'B to B Login',
    ];

    /**
     * @var ServiceClient
     */
    private ServiceClient $service;

    /**
     * @param ServiceClient $service
     */
    public function __construct(ServiceClient $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::ClientLogin);
    }

    public function args(): array
    {
        return [
            'email_or_account' => [
                'name' => 'email_or_account',
                'type' => Type::string(),
                'rules' => ['required','max:255'],
            ],
            'password' => [
                'name' => 'password',
                'type' => Type::string(),
                'rules' => ['required','max:255'],
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws \Throwable
     */
    public function resolve($root, $args): array
    {
        return $this->service->login($args);
    }
}
