<?php

namespace App\GraphQL\Mutations\Client;

use App\Models\Client;
use App\Services\ErpAuthService;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Mutation;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\ClientType;
use Stephenchenorg\BaseFilamentPlugin\Service\Auth\ServiceClient;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class ClientMeUpdateMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::ClientMeUpdate,
        'description' => 'Update client profile',
    ];

    /**
     * @var ServiceClient
     */
    private ServiceClient $service;

    /**
     * @param ServiceClient $service
     */
    public function __construct(ServiceClient $service)
    {
        $this->service = $service;
    }


    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [
            'name' => [
                'name' => 'name',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:50'],
            ],
            'email' => [
                'name' => 'email',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:70', 'email'],
            ],
            'id_number' => [
                'name' => 'id_number',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:10', 'regex:/^[A-Z][12]\d{8}$/'],
            ],
            'birthday' => [
                'name' => 'birthday',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required','date'],
            ],
            'gender' => [
                'name' => 'gender',
                'type' => Type::nonNull(Type::int()),
                'rules' => ['required','numeric', 'max:255', 'in:0,1,2'],
            ],
            'phone1' => [
                'name' => 'phone1',
                'type' => Type::string(),
                'rules' =>[
                    'required',
                    'max:10',
                    function ($attribute, $value, $fail)
                    {
                        if (!CCUtility::validatePhoneDomestic($value)) {
                            $fail('請符合09開頭電話號碼。');
                        }
                    },
                ],
            ],
            'phone2' => [
                'name' => 'phone2',
                'type' => Type::string(),
                'rules' =>[
                    'required',
                    'max:10',
                    function ($attribute, $value, $fail)
                    {
                        if (!CCUtility::validatePhoneDomestic($value)) {
                            $fail('請符合09開頭電話號碼。');
                        }
                    },
                ],
            ],
            'mobile' => [
                'name' => 'mobile',
                'type' => Type::string(),
                'rules' => ['required', 'max:20'],
            ],
            'address' => [
                'name' => 'address',
                'type' => Type::nonNull(Type::string()),
                'rules' => ['required', 'max:255'],
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $client = $this->service->update($args);
        $erpAuthService = new ErpAuthService();
        $erpAuthService->updateToErp($client);
        return '更新成功';
    }
}
