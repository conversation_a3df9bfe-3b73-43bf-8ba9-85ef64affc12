<?php

namespace App\Http\Controllers;

use Illuminate\View\View;
use Stephenchenorg\BaseFilamentPlugin\Http\Controllers\ECPayController as BaseECPayController;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use \Illuminate\Http\Request;

class ECPayController extends BaseECPayController
{

    public function checkout(Request $request):View
    {
        $orderKey = $request->input('order_key');

        if(!$orderKey) {
            abort(404);
        }

        // 根據 order_key 查找訂單
        $order = Order::query()->where('order_key', $orderKey)->first();

        if(!$order) {
            abort(404);
        }

        // 檢查 orderable 是否為 Client 類型
        $notAllowedTypes = [
            \Stephenchenorg\BaseFilamentPlugin\Models\Client::class,
            \App\Models\Client::class
        ];

        if (in_array($order->orderable_type, $notAllowedTypes)) {
            abort(404);
        }


        // 如果檢查通過，調用父類的 checkout 方法
        return parent::checkout($request);
    }

}
