<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification as BaseProductSpecification;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class ProductSpecification extends BaseProductSpecification
{

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($spec) {
            $spec->product_category_id = $spec->product->product_category_id;
        });
    }

    /**
     * Get all customers that have special pricing for this product specification.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphToMany
     */
    public function customers(): MorphToMany
    {
        return $this->morphedByMany(
            Customer::class,
            'specializable',
            'specializable_product_specifications'
        )
        ->using(SpecializablePivot::class)
        ->withPivot('special_price')
        ->withTimestamps();
    }

    /**
     * Get all clients that have special pricing for this product specification.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphToMany
     */
    public function clients(): MorphToMany
    {
        return $this->morphedByMany(
            Client::class,
            'specializable',
            'specializable_product_specifications'
        )
        ->using(SpecializablePivot::class)
        ->withPivot('special_price')
        ->withTimestamps();
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(
            ProductCategory::class,
            'product_category_id'
        );
    }

}
