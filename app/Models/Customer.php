<?php

namespace App\Models;

use Stephenchenorg\BaseFilamentPlugin\Models\Customer as BaseCustomer;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Customer extends BaseCustomer
{
    protected $guarded = [];

    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class,'sale_code','code');
    }

    /**
     * Get the special product specifications for the customer.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphToMany
     */
    public function specialProductSpecifications(): MorphToMany
    {
        return $this->morphToMany(
            ProductSpecification::class,
            'specializable',
            'specializable_product_specifications'
        )
        ->using(SpecializablePivot::class)
        ->withPivot([
            'special_price'
        ])
        ->withTimestamps();
    }

    public function orders(): MorphMany
    {
        return $this->morphMany(Order::class, 'orderable');
    }
}
