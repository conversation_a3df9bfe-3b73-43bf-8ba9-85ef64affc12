<?php

namespace App\Listeners;

use App\Events\OrderCreated;
use App\Services\ErpOrderService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;

class OrderErpListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * ERP 訂單服務
     *
     * @var ErpOrderService
     */
    protected ErpOrderService $erpOrderService;

    /**
     * 創建事件監聽器實例
     *
     * @param ErpOrderService $erpOrderService
     */
    public function __construct(ErpOrderService $erpOrderService)
    {
        $this->erpOrderService = $erpOrderService;
    }

    /**
     * 處理事件
     *
     * @param OrderCreated $event
     * @return void
     * @throws Exception
     */
    public function handle(OrderCreated $event): void
    {
        try {
            $this->updateErpApi($event->order);
        } catch (Exception $e) {
            Log::error('ERP 訂單同步失敗', [
                'order_id' => $event->order->id,
                'order_key' => $event->order->order_key,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 更新訂單到 ERP 系統
     *
     * @param Order $order
     * @return Order
     * @throws Exception
     */
    public function updateErpApi(Order $order): Order
    {
        // 準備主檔資料
        $mainData = $this->erpOrderService->buildMainData($order);

        // 準備明細資料
        $details = [];
        foreach ($order->items as $item) {
            $details[] = $this->erpOrderService->buildDetailData($item);
        }

        // 準備平台資料
        $platformData = $this->erpOrderService->buildPlatformData($order);

        // 建立完整訂單資料
        $orderData = $this->erpOrderService->buildOrderData(
            main: $mainData,
            details: $details,
            platformData: $platformData
        );

        // 發送訂單到 ERP
        $result = $this->erpOrderService->createOrder($orderData);
        $order->payload = json_encode($result);
        $order->erpCode = $result['OrderNo'] ?? null;
        $order->save();

        return $order;
    }
}
