<?php

namespace App\Services;


use App\Models\Sale;
use Exception;
use Illuminate\Contracts\Auth\Authenticatable;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Enum\EnumPriceType;


class ErpAuthService
{
    private string $providerNumber = 'TMSA003291';
    private string $key = '97790402-FF2F-47B4-8C2F-8F2C36AAC131';
    private string $customerQueryUrl = 'https://tmsapi.ktnet.com.tw/Cust/QueryList';
    private string $customerUpdateUrl = 'https://tmsapi.ktnet.com.tw/Cust/Add';

    public function queryAuth(array $authCodes)
    {
        // @TODO: 串接 https://tmsapi.ktnet.com.tw/Docs/Cust/QueryList 客戶資料查詢 /Cust/QueryList
        // @TODO: 要先到概要去看 https://tmsapi.ktnet.com.tw/Docs/Main/Sign 簽名方式

        // 時間戳
        $time = Carbon::now('Asia/Taipei')->addHours(8)->timestamp;

        // 簽章加密字串
        $body = [
            'CCODES' => $authCodes,
        ];
        $encode = json_encode($body);
        $originString = "{$this->providerNumber}{$time}{$encode}";
        $signString = strtoupper(hash_hmac('sha512', $originString, $this->key));


        $headers = [
            'api-pno' => $this->providerNumber,
            'api-Timestamp' => $time,
            'api-ClientSign' => $signString,
            'Content-Type' => 'application/json',
        ];
        $response = Http::withHeaders($headers)
            ->post($this->customerQueryUrl, $body);

        $result = $response->json();

        // 檢查是否有錯誤
        if ($result['Status'] === 'E') {
            throw new \Exception(
                "API 錯誤：{$result['ErrorMessage']} (錯誤代碼：{$result['ErrorCode']})"
            );
        }

        // 如果成功，回傳客戶資料
        return $result['Details'] ?? [];
    }

    public function updateFromErp(array $authCodes): void
    {
        $authResults = $this->queryAuth($authCodes);

        foreach ($authResults as $value) {

            try {

                DB::beginTransaction();
                $firstDigit = substr($value['CCODE'], 0, 1);
                if(!in_array($firstDigit,["1","2","3"])){
                    continue;
                }
                if($firstDigit == "1" || $firstDigit == "3") {
                    $model = Client::query()
                        ->where('code', '=', $value['CCODE'])
                        ->first();
                }

                if($firstDigit == "2") {

                    $model = Customer::query()
                        ->where('code', '=', $value['CCODE'])
                        ->first();
                }

                $createAttributes = [
                    'name'                      => $value['CNAM2'] ?? null,
                    'gender'                    => $value['Sex'] ?? null,
                    'address'                   => $value['CADD1'] ?? null,
                    'phone1'                    => $value['CTELE'] ?? null,
                    'phone2'                    => $value['CTEL2'] ?? null,
                    'mobile'                    => $value['CPHONE'] ?? null,
                    'web_account'               => $value['CUSTID'] ?? null,
                    'password'                  => $value['CUSTPW'] ?? null,
                    'id_number'                 => $value['CIDNO'] ?? null,
                    'birthday'                  => (!empty($value['CBIRD']) && strtotime($value['CBIRD'])) ? $value['CBIRD'] : null,
                    // 其他只在建立時設定的欄位...
                ];

                $updateAttributes = [
                    'company_name'              => $value['CNAM1'] ?? $model->company_name,
                    'invoice_address'           => $value['CADD2'] ?? $model->invoice_address,
                    'billing_address'           => $value['CADD3'] ?? $model->billing_address,
                    'phone1_extension'          => $value['CPOS1'] ?? $model->phone1_extension,
                    'phone2_extension'          => $value['CPOS2'] ?? $model->phone2_extension,
                    'fax'                       => $value['CTFAX'] ?? $model->fax,
                    'contact_person'            => $value['CMAN1'] ?? $model->contact_person,
                    'vat'                       => $value['CTCOD'] ?? $model->vat,
                    'sale_code'                 => $value['CSCOD'] ?? $model->sale_code ?? ($firstDigit == 2) ? 'A001' : '0609',
                    'email'                     => !empty($value['EMAIL']) ? $value['EMAIL'] : $model->email ?? null,
                    'invoice_issue_method'      => $value['OFFER'] ?? $model->invoice_issue_method,
                    'invoice_title'             => $value['CNAM3'] ?? $model->invoice_title,
                    'price_type'                => EnumPriceType::fromNumberToValue($value['CUNIT']) ?? $model->price_type,
                    'invoice_tax_type'          => $value['CTAX1'] ?? $model->invoice_tax_type,
                    'free_shipping_threshold'   => $value['CwebM'] ?? $model->free_shipping_threshold,
                    'basic_shipping_fee'        => $value['CwebS'] ?? $model->basic_shipping_fee,
                    'bonus_points'              => $value['BonusPoint'] ?? $model->bonus_points,
                    'pos_bonus'                 => $value['PosBonus'] ?? $model->pos_bonus,
                ];


                if(!empty($value['CSCOD'])){
                    Sale::query()->firstOrCreate(
                        ['code' => $value['CSCOD']],
                        [
                            'code' => $value['CSCOD'],
                            'name' => $value['CSCOD']
                        ]
                    );
                }
                if (!$model) {
                    if($firstDigit == "2"){
                        $model = Customer::create($createAttributes + $updateAttributes);
                    }else{
                        $model = Client::create($createAttributes + $updateAttributes);
                    }
                } else {
                    $model->update($updateAttributes);
                }


                DB::commit();

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;

            }

        }
    }



    /**
     * 建立或更新客戶資料到 ERP 系統
     *
     * @param Customer $customer 客戶模型
     * @return array 回傳結果
     * @throws Exception 當 API 呼叫失敗時拋出
     */
    public function updateToErp(Customer|Client|Authenticatable $customer): array
    {
        // 準備要傳送的客戶資料
        $customerData = [
            'AutoCCODE' => 0,
            'CCODE' => $customer->code,
            'CNAM1' => $customer->name,
            'EMAIL' => $customer->email,
            'CSCOD' => $customer->sale_code,
        ];

        // 時間戳
        $time = Carbon::now('Asia/Taipei')->addHours(8)->timestamp;

        // 準備請求資料
        $body = [
            'ProNum' => $this->providerNumber,
            'Main' => array_merge([
                'EditItem' => 2, // 2: 由系統判斷是否新增或更新
            ], $customerData)
        ];

        // 簽章加密字串
        $encode = json_encode($body);
        $originString = "{$this->providerNumber}{$time}{$encode}";
        $signString = strtoupper(hash_hmac('sha512', $originString, $this->key));

        // 設定請求標頭
        $headers = [
            'api-pno' => $this->providerNumber,
            'api-Timestamp' => $time,
            'api-ClientSign' => $signString,
            'Content-Type' => 'application/json',
        ];

        // 發送請求
        $response = Http::withHeaders($headers)
            ->post($this->customerUpdateUrl, $body);

        $result = $response->json();

        // 更新客戶的 payload 資訊
        $customer->payload = json_encode($result);
        $customer->payload_status = $result['Status'] === 'S' ? 1 : 2;
        $customer->save();

        return $result;
    }


}
