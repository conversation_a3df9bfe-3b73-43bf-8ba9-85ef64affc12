<?php

namespace App\Services;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Http;
use <PERSON>chenorg\BaseFilamentPlugin\Models\Order;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\OrderItem;

class ErpOrderService
{
    private string $providerNumber = 'TMSA003291';
    private string $key = '97790402-FF2F-47B4-8C2F-8F2C36AAC131';
    private string $orderAddUrl = 'https://tmsapi.ktnet.com.tw/Order/Add';

    /**
     * 建立新訂單
     *
     * @param array $orderData 訂單資料
     * @return array 回傳結果
     * @throws \Exception 當 API 呼叫失敗時拋出
     */
    public function createOrder(array $orderData): array
    {
        // 時間戳
        $time = Carbon::now('Asia/Taipei')->addHours(8)->timestamp;

        // 準備請求資料
        $body = [
            'ProNum' => $this->providerNumber,
            'Main' => $orderData['main'] ?? [],
            'Details' => $orderData['details'] ?? [],
            'PlatformData' => $orderData['platform_data'] ?? null,
        ];

        // 簽章加密字串
        $encode = json_encode($body);
        $originString = "{$this->providerNumber}{$time}{$encode}";
        $signString = strtoupper(hash_hmac('sha512', $originString, $this->key));

        // 設定請求標頭
        $headers = [
            'api-pno' => $this->providerNumber,
            'api-Timestamp' => $time,
            'api-ClientSign' => $signString,
            'Content-Type' => 'application/json',
        ];

        // 發送請求
        $response = Http::withHeaders($headers)
            ->post($this->orderAddUrl, $body);

        $result = $response->json();

        // 檢查是否有錯誤
//        if ($result['Status'] === 'E') {
//            throw new \Exception(
//                "API 錯誤：{$result['ErrorMessage']} (錯誤代碼：{$result['ErrorCode']})"
//            );
//        }

        return $result;
    }

    /**
     * 建立訂單資料結構
     *
     * @param array $main 主檔資料
     * @param array $details 明細資料
     * @param array|null $platformData 平台資料
     * @return array 訂單資料結構
     */
    public function buildOrderData(array $main, array $details, ?array $platformData = null): array
    {
        return [
            'main' => $main,
            'details' => $details,
            'platform_data' => $platformData,
        ];
    }

    /**
     * 建立訂單主檔資料
     *
     * @param Order $order
     * @return array 主檔資料
     */
    public function buildMainData(Order $order): array
    {


        return [

            'ODATE' => $order->created_at->format('Y/m/d'),
            'OCCOD' => $order->orderable->code,
            'OCOD4' => $order->order_key,
            'OSCOD' => $order->sale_code,//業務代號
            'OTax' => '9', // 預設混稅
            'UseInvoice' => '1', // 預設開發票
            'ToSaleOrder' => '0', // 預設不轉銷貨單
            'ReceiveName' => $order->name,
            'OPNAM' => $order->name,
            'OTEL' => $order->phone,
            'OFAX' => '',
            'MobileNO' => $order->phone,
            'OADD1' => $this->formatAddress($order),
            'OADD2' => $this->formatAddress($order),
            'OBAK1' => $order->remark,
            'ReceiveTEL' => $order->phone,
            'ReceiveMobile' => $order->phone,
            'ReceiveZIPCode' => $order->postal_code,
            'ReceiveAddr' => $this->formatAddress($order),
            'ReceiveCTCOD' => $order->vat,
            'ReceiveComName' => $order->invoice_title,
            'ReceiveEmail' => $order->email,
            'CarrierType' => $order->carrier_type,
            'CarrierId2' => $order->carrier_number
        ];

    }

    /**
     * 建立訂單明細資料
     *
     * @param OrderItem $item
     * @return array 明細資料
     */
    public function buildDetailData(OrderItem $item): array
    {
        return [
            'ICODE' => $item->specification->sku,
            'Qty' => $item->quantity,
            'Price' => $item->unit_price,
            'Stock' => 'A001',
            'TaxBA' => 'B', // 預設稅前
            'DTaxType' => '1', // 預設稅別，可以根據需求調整
        ];
    }

    /**
     * 建立平台資料
     *
     * @param Order $order
     * @return array 平台資料
     */
    public function buildPlatformData(Order $order): array
    {
        return [
            'FromCode' => 'TMS',
            'BuyerName' => $order->name,
            'BuyerPhone' => $order->phone,
            'BuyerAddress' => $this->formatAddress($order),
            'ReceiveName' => $order->name,
            'ReceiveAddress' => $this->formatAddress($order),
            'ReceivePhone' => $order->phone,
            'OrderDate' => $order->created_at->format('Y/m/d H:i'),
            'OrderNumber' => $order->order_key,
            'Total' => $order->total_amount,
        ];
    }


    /**
     * 格式化地址
     *
     * @param Order $order
     * @return string
     */
    private function formatAddress(Order $order): string
    {
        return sprintf(
            '%s%s%s%s',
            $order->postal_code,
            $order->city,
            $order->district,
            $order->address_line1
        );
    }
}
