<?php

namespace App\Services;

use App\Models\Client;
use App\Models\Customer;
use App\Models\ProductSpecification;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceProductSpecificationInterface;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Models\OrderItem;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;


class ServiceOrder extends \Stephenchenorg\BaseFilamentPlugin\Service\ServiceOrder
{
    protected ErpOrderService $erpOrderService;


    public function __construct(ServiceProductSpecificationInterface $serviceProductSpecification)
    {
        $this->erpOrderService = new ErpOrderService();
        parent::__construct($serviceProductSpecification);
    }


    /**
     * 建立訂單
     *
     * @param array $data 包含完整的結帳資料
     * @return Order 新建立的訂單物件
     * @throws Exception 如果建立訂單失敗，會拋出異常
     */
    public function createOrder(array $data): Order
    {
        DB::beginTransaction();

        try {

            // 檢查物流方式
            $this->checkShippingMethod($data['items'],$data['shipping_method']);

            // 檢查庫存並鎖定
            $this->checkInventory($data['items']);

            // 檢查登入狀態並取得 orderable 資訊
            $orderableData = $this->getOrderableData();

            // 計算訂單金額
            $orderData = $this->calculateOrderAmounts($data['items'], $data['shipping_method'], $data['invoice_method']);

            // 產生訂單編號
            $orderKey = $this->generateOrderKey();

            $redeemPoints = $this->validateRedeemPoints($data);


            // 建立訂單
            $order = Order::create([
                'orderable_type' => $orderableData['type'],
                'orderable_id' => $orderableData['id'],
                'order_key' => $orderKey,
                'shipping_cost' => $orderData['shipping_cost'],
                'item_amount' => $orderData['item_amount'],
                'tax' => $orderData['tax'],
                'total_amount_taxed' => $orderData['total_amount_taxed'],
                'total_amount_untaxed' => $orderData['total_amount_untaxed'],
                'payment_method' => $data['payment_method'],
                'payment_status' => EnumPaymentStatus::UNPAID->value,
                'shipping_method' => $data['shipping_method'],
                'shipping_status' => EnumShippingStatus::UNSHIPPED->value,
                'status' => EnumOrderStatus::PENDING->value,
                'name' => $data['name'],
                'phone' => $data['phone'],
                'email' => $data['email'],
                'payment_gateway' => config('cs.payment_gateway') ?? null,

                // 地址資訊
                'store_address_id' => $data['store_address_id'] ?? null,
                'country_code' => $data['country_code'] ?? null,
                'state' => $data['state'] ?? null,
                'city' => $data['city'] ?? null,
                'district' => $data['district'] ?? null,
                'postal_code' => $data['postal_code'] ?? null,
                'address_line1' => $data['address_line1'] ?? null,
                'address_line2' => $data['address_line2'] ?? null,

                // 發票資訊
                'invoice_method' => $data['invoice_method'],
                'carrier_value' => $data['carrier_value'] ?? null,
                'invoice_address' => $data['invoice_address'] ?? null,
                'vat' => $data['vat'] ?? null,
                'invoice_title' => $data['invoice_title'] ?? null,

                // 紅利點數
                'redeem_points' => $redeemPoints,
            ]);

            // 建立訂單項目
            $this->createOrderItems($order, $data['items']);


            // 同步到 ERP
            if (!empty($order->orderable)){
                $this->updateErpApi($order);
            }


            DB::commit();
            return $order->load('items');

        }catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 計算訂單金額
     */
    public function calculateOrderAmounts(array $items, string $shippingMethod, string $invoiceMethod): array
    {
        $itemAmount = 0;

        foreach ($items as $item) {
            $specification = ProductSpecification::findOrFail($item['product_specification_id']);
            $sellingPrice = ServiceProduct::getSpecSellingPrice($specification);
            $itemAmount += $sellingPrice * $item['quantity'];
        }

        $shippingCost = $this->getShippingCost($items,$shippingMethod);
        $totalAmountUntaxed = $itemAmount + $shippingCost;

        // 檢查是否為三聯發票且需要加稅
        $shouldApplyTax = $invoiceMethod === EnumInvoiceMethod::TRIPLICATE->value &&
                         config('cs.triplicate_tax', true);

        $totalAmountTaxed = $shouldApplyTax ?
                           round($totalAmountUntaxed * 1.05, 2) :
                           $totalAmountUntaxed;

        $tax = $totalAmountTaxed - $totalAmountUntaxed;

        return [
            'item_amount' => $itemAmount,
            'shipping_cost' => $shippingCost,
            'tax' => $tax,
            'total_amount_taxed' => $totalAmountTaxed,
            'total_amount_untaxed' => $totalAmountUntaxed,
        ];
    }



    /**
     * 建立訂單項目
     */
    protected function createOrderItems(Order $order, array $items): void
    {
        foreach ($items as $item) {
            $specification = ProductSpecification::findOrFail($item['product_specification_id']);
            $sellingPrice = ServiceProduct::getSpecSellingPrice($specification);
            $lang = app()->getLocale() ?? ServiceLanguage::getDefaultLanguage();
            $title = $specification->translations->where('lang', $lang)->first()->title ?? $specification->translations->first()->title;

            OrderItem::create([
                'order_id' => $order->id,
                'product_specification_id' => $item['product_specification_id'],
                'title' => $title,
                'quantity' => $item['quantity'],
                'unit_price' => $sellingPrice,
                'total_amount' => $sellingPrice * $item['quantity'],
            ]);
        }
    }


    /**
     * 產生訂單編號
     */
    protected function generateOrderKey(): string
    {

        $isClient = Auth::guard('clients')->check();
        $prefix = $isClient ? 'B' : 'C';
        $date = date('md');
        $unique = uniqid();
        return "{$prefix}{$date}{$unique}";
    }

    /**
     * 更新訂單到 ERP 系統
     *
     * @param Order $order
     * @return Order
     * @throws Exception
     */
    public function updateErpApi(Order $order): Order
    {
        // 準備主檔資料
        $mainData = $this->erpOrderService->buildMainData($order);

        // 準備明細資料
        $details = [];
        foreach ($order->items as $item) {
            $details[] = $this->erpOrderService->buildDetailData($item);
        }

        // 準備平台資料
        $platformData = $this->erpOrderService->buildPlatformData($order);

        // 建立完整訂單資料
        $orderData = $this->erpOrderService->buildOrderData(
            main: $mainData,
            details: $details,
            platformData: $platformData
        );

        // 發送訂單到 ERP
        $result = $this->erpOrderService->createOrder($orderData);
        $order->payload = json_encode($result);
        $order->erpCode = $result['OrderNo'] ?? null;
        $order->save();

        return $order;
    }


    public function validateRedeemPoints(array $data): int
    {
        return 0;
    }

}
