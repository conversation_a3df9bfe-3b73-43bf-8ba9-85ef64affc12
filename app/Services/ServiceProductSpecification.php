<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;

class ServiceProductSpecification extends \Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductSpecification
{

    public function getListingPrice(ProductSpecification $productSpecification): ?float
    {

        $isB2B = config('cs.app') === 'pfl';
        $isB2BLoggedIn = Auth::guard('clients')->check();
        $isB2CLoggedIn = Auth::guard('customers')->check();

        if (!$isB2B) {
            return $productSpecification->listing_price;
        }

        if (!$isB2BLoggedIn) {
            return null;
        }

        return $productSpecification->listing_price;
    }


    public function getSellingPrice(ProductSpecification $productSpecification): ?float
    {
        // 已經在 query時 eager loading customers/clients 並且只有一筆

        $isB2B = config('cs.app') === 'pfl';
        $isB2C = config('cs.app') !== 'pfl';
        $isB2BLoggedIn = Auth::guard('clients')->check();
        $isB2CLoggedIn = Auth::guard('customers')->check();



        if ($isB2C && !$isB2CLoggedIn) {
            return $productSpecification->selling_price4;
        }

        if ($isB2B && !$isB2BLoggedIn) {
            return null;
        }

        if ($isB2C) {
            $customer = Auth::guard('customers')->user();
            $customerRelationEntry = $productSpecification->customers->where('id','=',$customer->id)->first();
            return $customerRelationEntry?->pivot?->special_price ?? $productSpecification->{$customer->price_type};
        }


        $client = Auth::guard('clients')->user();
        $clientRelationEntry = $productSpecification->clients->where('id','=',$client->id)->first();
        return $clientRelationEntry?->pivot?->special_price ?? $productSpecification->{$client->price_type};
    }

}
