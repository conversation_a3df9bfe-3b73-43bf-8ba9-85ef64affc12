<?php

namespace App\Services\Auth;

use App\Models\Sale;
use App\Services\ErpAuthService;
use Stephenchenorg\BaseFilamentPlugin\Mail\Customer\CustomerWelcomeRegister;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Models\Verification;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Throwable;

final class ServiceCustomerRegister
{

    /**
     * 註冊新用戶
     *
     * @param array $args 註冊參數，包含 email（信箱）、password（密碼）和 code（驗證碼）
     * @return void
     * @throws AuthenticationException 當驗證碼錯誤或過期時拋出
     * @throws Throwable
     */
    public function register(array $args): void
    {
        try {

            DB::beginTransaction();

            $code = $args['code'];
            $first = Verification::query()
                ->where('target', $args['email'])
                ->where('verifiable_type', '=', Customer::class)
                ->where('code', $code)
                ->first();

            if (!$first) {
                throw new AuthenticationException(__('auth.verification_code_error'));
            }

            if (!empty($first->verified_at)) {
                throw new AuthenticationException(__('auth.verification_code_used'));
            }

            // 檢查驗證碼是否過期
            if ($first->expired_at && Carbon::now()->isAfter($first->expired_at)) {
                throw new AuthenticationException(__('auth.verification_code_expired'));
            }

            // 檢查信箱是否已被註冊
            $existingUser = Customer::query()->firstWhere('email', $args['email']);
            if ($existingUser) {
                throw new AuthenticationException(__('auth.email_already_registered'));
            }

            // 標記驗證碼已被使用
            $first->verified_at = Carbon::now();

            // B2C 空的固定 A001
            $saleCode = "A001";
            if($args['sale_code']){
                $sale = Sale::query()->where('code', $args['sale_code'])->first();
                $saleCode = $sale->code;
            }

            $user = Customer::query()->create([
                'email' => $args['email'],
                'password' => $args['password'],
                'email_verified_at' => Carbon::now()->toDateTimeString(),
                'code' => $this->createCode(),
                'sale_code' => $saleCode,
            ]);


            $first->verifiable_id = $user->id;
            $first->save();

            DB::commit();

            Mail::to($user->email)->send(new CustomerWelcomeRegister($user));

            logger("=============== 新客戶註冊 ===============");
            $this->updateErpApi($user);

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function createCode()
    {
        do {
            $code = strtoupper(Str::random(6));        // e.g. "A1B2C3"
            $fullCode = "2-官網-{$code}";              // e.g. "2-官網-A1B2C3"
        } while (Customer::query()->where('code', $fullCode)->exists());

        return $fullCode;
    }

    public function updateErpApi(Customer $customer): void
    {
        $erpAuthService = new ErpAuthService();
        $erpAuthService->updateToErp($customer);
    }
}
