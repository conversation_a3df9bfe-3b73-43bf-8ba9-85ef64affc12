<?php

namespace App\Services;

use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductTranslation;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;



class ErpService
{
    private string $providerNumber = 'TMSA003291';
    private string $key = '97790402-FF2F-47B4-8C2F-8F2C36AAC131';
    private string $productQueryUrl = 'https://tmsapi.ktnet.com.tw/product/QueryList';
    private string $stockQueryUrl = 'https://tmsapi.ktnet.com.tw/Stock/QueryList';

    public function queryProducts(array $productCodes)
    {
        // @TODO: 串接 https://tmsapi.ktnet.com.tw/Docs/Product/QueryList 產品資料查詢 /Product/QueryList
        // @TODO: 要先到概要去看 https://tmsapi.ktnet.com.tw/Docs/Main/Sign 簽名方式

        // 時間戳
        $time = Carbon::now('Asia/Taipei')->addHours(8)->timestamp;

        // 簽章加密字串
        $body = [
            'ICODES' => $productCodes,
        ];
        $encode = json_encode($body);
        $originString = "{$this->providerNumber}{$time}{$encode}";
        $signString = strtoupper(hash_hmac('sha512', $originString, $this->key));


        $headers = [
            'api-pno' => $this->providerNumber,
            'api-Timestamp' => $time,
            'api-ClientSign' => $signString,
            'Content-Type' => 'application/json',
        ];
        $response = Http::withHeaders($headers)
            ->post($this->productQueryUrl, $body);

        $result = $response->json();

        // 檢查是否有錯誤
        if ($result['Status'] === 'E') {
            throw new \Exception(
                "API 錯誤：{$result['ErrorMessage']} (錯誤代碼：{$result['ErrorCode']})"
            );
        }

        // 如果成功，回傳產品資料
        return $result['Details'] ?? [];
    }

    public function queryStocks(array $productCodes)
    {
        // 時間戳
        $time = Carbon::now('Asia/Taipei')->addHours(8)->timestamp;
        // 簽章加密字串
        $body = [
            'ICODES' => $productCodes,
        ];
        $encode = json_encode($body);
        $originString = "{$this->providerNumber}{$time}{$encode}";
        $signString = strtoupper(hash_hmac('sha512', $originString, $this->key));


        $headers = [
            'api-pno' => $this->providerNumber,
            'api-Timestamp' => $time,
            'api-ClientSign' => $signString,
            'Content-Type' => 'application/json',
        ];
        $response = Http::withHeaders($headers)
            ->post($this->stockQueryUrl, $body);

        $result = $response->json();

        // 檢查是否有錯誤
        if ($result['Status'] === 'E') {
            throw new \Exception(
                "API 錯誤：{$result['ErrorMessage']} (錯誤代碼：{$result['ErrorCode']})"
            );
        }

        // 如果成功，回傳庫存資料
        return $result['Details'] ?? [];
    }

    public function updateProducts(array $productCodes): void
    {

        $productResults = $this->queryProducts($productCodes);
        $stockResults = $this->queryStocks($productCodes);

        $productResults = collect($productResults)->keyBy('ICODE');
        $stockResults = collect($stockResults)->keyBy('ICODE');


        foreach ($productCodes as $code) {

            try {

                DB::beginTransaction();

                $product = Product::query()
                    ->where('part_number', '=', $code)
                    ->first();

                $product->update([
                    'is_newest' => $productResults[$code]['ITNEW'] ?? $product->is_newest,
                    'is_hottest' => $productResults[$code]['IMAIN'] ?? $product->is_hottest,
                    'status'    =>  $productResults[$code]['Istop'] == 1 ? 0 : 1,
                ]);

                ProductTranslation::query()
                    ->where('product_id', '=', $product->id)
                    ->where('lang', '=', 'zh_TW')
                    ->update([
                        'title' => $productResults[$code]['INAME'] ?? $product->title,
                        'content_1' => $productResults[$code]['T11_6'] ?? $product->content_1,
                        'content_2' => $productResults[$code]['T11_7'] ?? $product->content_2,
                        'content_3' => $productResults[$code]['T11_8'] ?? $product->content_3,
                    ]);

                $productSpec = ProductSpecification::query()
                    ->where('sku', '=', $productResults[$code]['ICODE'])
                    ->first();

                $productSpec->update([
                    'listing_price' => $productResults[$code]['IUNIT6'] ?? $productSpec->listing_price,
                    'selling_price' => $productResults[$code]['IUNIT'] ?? $productSpec->selling_price,
                    'selling_price2' => $productResults[$code]['IUNIT2'] ?? $productSpec->selling_price2,
                    'selling_price3' => $productResults[$code]['IUNIT3'] ?? $productSpec->selling_price3,
                    'selling_price4' => $productResults[$code]['IUNIT4'] ?? $productSpec->selling_price4,
                    'selling_price5' => $productResults[$code]['IUNIT5'] ?? $productSpec->selling_price5,
                    'ean' => $productResults[$code]['EAN13'] ?? $productSpec->ean,
                    'inventory' => round($stockResults[$code]['Num']) ?? $productSpec->inventory,
                ]);

                ProductSpecificationTranslation::query()
                    ->where('product_specification_id', '=', $productSpec->id)
                    ->where('lang', '=', 'zh_TW')
                    ->update([
                        'title' => $productResults[$code]['INAME'] ?? $productSpec->title,
                        'attr1' => $productResults[$code]['ATTR1'] ?? $productSpec->attr1,
                        'attr2' => $productResults[$code]['ATTR2'] ?? $productSpec->attr2,
                    ]);

                DB::commit();

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;

            }

        }
    }
}
