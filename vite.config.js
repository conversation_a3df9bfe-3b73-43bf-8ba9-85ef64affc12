import {defineConfig} from 'vite';
import laravel from 'laravel-vite-plugin';
import path from 'path';

export default defineConfig({
    resolve: {
        alias: {
            '@vendor': path.resolve(__dirname, 'vendor'),
            '@resources': path.resolve(__dirname, 'resources'),
        },
    },
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/css/filament/admin/theme.css',
                "resources/js/mermaid.js",
            ],
            refresh: true,
        }),
    ],
});
