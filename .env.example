
# --------------------------------------------------------------------------
# Application
# --------------------------------------------------------------------------

APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=Asia/Taipei
APP_URL=http://localhost
ASSET_URL=${APP_URL}

# 系統管理員最高權限帳號
APP_HIDDEN_SUPER_ADMIN_ACCOUNT="${CS_EXTRA_PLUGIN_NAME}<EMAIL>"
APP_HIDDEN_SUPER_ADMIN_PASSWORD="${CS_EXTRA_PLUGIN_NAME}besthaha666"

# 使用者最高權限帳號
APP_SUPER_ADMIN_ACCOUNT="${CS_EXTRA_PLUGIN_NAME}<EMAIL>"
APP_SUPER_ADMIN_PASSWORD="${CS_EXTRA_PLUGIN_NAME}-admin-666"

# 預設 B2C 客戶帳號
APP_SUPER_CUSTOMER_ACCOUNT="${CS_EXTRA_PLUGIN_NAME}<EMAIL>"
APP_SUPER_CUSTOMER_PASSWORD="${CS_EXTRA_PLUGIN_NAME}-admin-666"

# 預設 B2B 企業客戶帳號
APP_SUPER_CLIENT_ACCOUNT="${CS_EXTRA_PLUGIN_NAME}<EMAIL>"
APP_SUPER_CLIENT_PASSWORD="${CS_EXTRA_PLUGIN_NAME}-admin-666"

APP_LOCALE=zh_TW
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=zh_TW
APP_IMAGE_SIZE=2048

# Customize the languages that the system supports, order by priority

APP_LANGUAGES="zh_TW,en"

# 根據不同專案會有不同的 聯絡我們選項

APP_CONTACTS="contact,recruit"

# 根據不同專案會有不同的前台網址

APP_FRONTEND_URL=""
APP_MAINTENANCE_DRIVER=file

# APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

# --------------------------------------------------------------------------
# Log
# --------------------------------------------------------------------------

LOG_CHANNEL=daily
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# --------------------------------------------------------------------------
# Database
# --------------------------------------------------------------------------

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=base
DB_USERNAME=root
DB_PASSWORD=
DB_QUEUE="default"

# --------------------------------------------------------------------------
# Session
# --------------------------------------------------------------------------

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# --------------------------------------------------------------------------
# Frontend
# --------------------------------------------------------------------------

VITE_APP_NAME="${APP_NAME}"

# --------------------------------------------------------------------------
# Cache
# --------------------------------------------------------------------------

CACHE_STORE=file
CACHE_PREFIX=
MEMCACHED_HOST=127.0.0.1

# --------------------------------------------------------------------------
# Redis
# --------------------------------------------------------------------------

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# --------------------------------------------------------------------------
# Mail
# --------------------------------------------------------------------------

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# --------------------------------------------------------------------------
# AWS
# --------------------------------------------------------------------------

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=ap-northeast-1
AWS_BUCKET=k2j13kk1j2
AWS_ENDPOINT="https://s3.ap-northeast-1.amazonaws.com"
AWS_URL="https://k2j13kk1j2.s3.amazonaws.com"
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_PREFIX=local

# --------------------------------------------------------------------------
# TURNSTILE , below is a test key, for test purpose you should generate a new one
# --------------------------------------------------------------------------

TURNSTILE_SITE_KEY="1x00000000000000000000AA"
TURNSTILE_SECRET_KEY="1x0000000000000000000000000000000AA"

# --------------------------------------------------------------------------
# JWT Token
# --------------------------------------------------------------------------

JWT_SECRET=

# --------------------------------------------------------------------------
# Other
# --------------------------------------------------------------------------

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
FILAMENT_FILESYSTEM_DISK=local

# --------------------------------------------------------------------------
# CS
# --------------------------------------------------------------------------

# 控制是否可見
CS_ARTICLE_VISIBLE=true
CS_BANNER_VISIBLE=true
CS_CONTACT_VISIBLE=true
CS_FAQ_VISIBLE=true
CS_PRODUCT_VISIBLE=true
CS_BRAND_VISIBLE=true
CS_CUSTOMER_VISIBLE=true
CS_CLIENT_VISIBLE=true

# 控制熱門與最新的數量上限
CS_ARTICLE_HOTTEST_LIMIT=3
CS_PRODUCT_HOTTEST_LIMIT=3
CS_PRODUCT_CATEGORY_HOTTEST_LIMIT=3
CS_ARTICLE_NEWEST_LIMIT=3
CS_PRODUCT_NEWEST_LIMIT=3
CS_PRODUCT_CATEGORY_NEWEST_LIMIT=3

# 系統管理員最高權限帳號
CS_HIDDEN_SUPER_ADMIN_ACCOUNT="<EMAIL>"
CS_HIDDEN_SUPER_ADMIN_PASSWORD="admin"

# 使用者最高權限帳號
CS_SUPER_ADMIN_ACCOUNT="<EMAIL>"
CS_SUPER_ADMIN_PASSWORD="admin"

# 預設 B2C 客戶帳號
CS_SUPER_CUSTOMER_ACCOUNT="<EMAIL>"
CS_SUPER_CUSTOMER_PASSWORD="admin"

# 預設 B2B 企業客戶帳號
CS_SUPER_CLIENT_ACCOUNT="<EMAIL>"
CS_SUPER_CLIENT_PASSWORD="admin"

# 是否讓文章與產品共享標籤
CS_SHARE_TAG=false
# 產品有哪些 type
CS_PRODUCT_TYPES="product,service"
# 控制是否自動產生圖片的 Variants
CS_IMAGE_VARIANTS=false
# 控制後台有哪些語系
CS_LANGUAGES="zh_TW,en"
# 寄信時使用的店家名稱
CS_MAIL_NAME="豹發力"
# 接受哪些付款方式
CS_ACCEPT_PAYMENT_METHODS="NONE,ALL,CREDIT_CARD,ATM,WEB_ATM,CVS,BARCODE"
# 接受哪些開立發票的方式
CS_ACCEPT_INVOICE_METHODS="none,duplicate,triplicate,mobile_barcode,citizen_card"
# 接受哪些運送方式
CS_ACCEPT_SHIPPING_METHODS="none,delivery,ok_mart,seven_eleven,family_mart,hi_life"
# 三聯單時 是否外加營業稅
CS_TRIPLICATE_TAX=true
# 金流模式
CS_PAYMENT_MODE="development"
# 付款成功重導向頁面
CS_ORDER_RESULT_URL="http://localhost:8000/order-result"
# 是否使用預設金流路由
CS_USE_DEFAULT_PAYMENT_ROUTES=true

# --------------------------------------------------------------------------
# 綠界
# --------------------------------------------------------------------------

ECPAY_MERCHANT_ID="3002607"
ECPAY_HASH_KEY="pwFHCqoQZGmho4w6"
ECPAY_HASH_IV="EkRm7iFT261dpevs"
ECPAY_INVOICE_HASH_KEY=
ECPAY_INVOICE_HASH_IV=


