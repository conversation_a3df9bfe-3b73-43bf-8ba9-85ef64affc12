<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Order;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class OrderPreflightType extends GraphQLType
{
    protected $attributes = [
        'name' => EnumNames::OrderPreflight,
        'description' => 'Order preflight information including amount and shipping methods',
    ];

    public function fields(): array
    {
        return [
            'amount' => [
                'type' => Type::nonNull(GraphQL::type(EnumNames::OrderAmount)),
                'description' => 'The calculated order amount details',
            ],
            'shippingMethods' => [
                'type' => Type::nonNull(Type::listOf(GraphQL::type(EnumNames::ShippingMethod))),
                'description' => 'Available shipping methods with costs',
            ],
        ];
    }
}
