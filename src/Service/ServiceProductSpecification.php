<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\ProductAttributeItem;

final class ServiceProductSpecification
{
    public static function getCombinationName(?string $combinationKey,string $lang = null): ?string
    {
        if(empty($combinationKey)) return null;

        $attributeItemIds = explode('-', $combinationKey);

        $items = ProductAttributeItem::query()
            ->with([
                'translations' => function ($query) {
                    $query->where('lang', '=', $lang ?? ServiceLanguage::getDefaultLanguage());
                }
            ])
            ->whereIn('id', $attributeItemIds)
            ->get();

        return implode(", ", $items->map(function ($item) {
            return $item->translations->first()->title;
        })->toArray());

    }


    public static function validateCombinationKey(string $combinationKey, Product $product): bool
    {
        $attributesLen = $product->attributes->count();
        $combinationKeyLen = count(explode('-', $combinationKey));

        if ($attributesLen !== $combinationKeyLen) return false;

        $specCount = $product
            ->specifications
            ->where('combination_key', $combinationKey)
            ->count();


        if($specCount > 1) return false;

        return true;
    }


}
