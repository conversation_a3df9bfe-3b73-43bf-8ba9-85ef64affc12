<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\URL;
use Illuminate\View\View;
use JsonException;
use Stephenchenorg\BaseFilamentPlugin\Contracts\PaymentServiceInterface;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Http\Resources\OrderResource;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceOrder;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitResponse;
use TsaiYiHua\ECPay\Services\StringService;

class ECPayController extends Controller
{
    use CCTraitResponse;

    protected ServiceOrder $serviceOrder;
    protected PaymentServiceInterface $paymentService;

    public function __construct(ServiceOrder $serviceOrder, PaymentServiceInterface $paymentService)
    {
        $this->serviceOrder = $serviceOrder;
        $this->paymentService = $paymentService;
    }

    public function notifyUrl(Request $request): string
    {
        $serverPost = $request->post();
        Log::info('ECPay notify', $serverPost);

        $checkMacValue = $serverPost['CheckMacValue'] ?? '';
        unset($serverPost['CheckMacValue']);
        $checkCode = StringService::checkMacValueGenerator($serverPost);

        if ($checkMacValue !== $checkCode) {
            Log::warning('ECPay MAC validation failed', [
                'expected' => $checkCode,
                'received' => $checkMacValue,
            ]);
            return '0|FAIL';
        }

        try {
            DB::beginTransaction();

            // 先鎖住 避免同時更新
            $order = Order::query()
                ->where('order_key', $serverPost['MerchantTradeNo'])
                ->lockForUpdate()
                ->first();

            if (!$order) {
                Log::warning('Order not found for MerchantTradeNo', [
                    'MerchantTradeNo' => $serverPost['MerchantTradeNo'],
                ]);
                DB::rollBack();
                return '0|FAIL';
            }

            $existingPayload = $order->payload ? json_decode($order->payload, true) : [];
            $existingPayload[] = $serverPost;

            $bkCode = $serverPost['RtnCode'] ?? null;
            $paymentStatus = $bkCode === '1' ? EnumPaymentStatus::PAID->value : EnumPaymentStatus::FAILED->value;


            $order->update([
                'bk_rtn_code' => $bkCode,
                'payload' => $existingPayload,
            ]);

            $this->serviceOrder->setOrderPaymentStatus($order, $paymentStatus);

            DB::commit();
            return '1|OK';

        } catch (\Throwable $e) {
            DB::rollBack();

            Log::error('ECPay notify processing error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return '0|FAIL';
        }
    }

    /**
     * @throws Exception
     */
    public function returnUrl(Request $request): View
    {
        $serverPost = $request->post();
        $receivedMac = $serverPost['CheckMacValue'] ?? '';
        unset($serverPost['CheckMacValue']);

        $calculatedMac = StringService::checkMacValueGenerator($serverPost);

        if ($receivedMac !== $calculatedMac) {
           throw new Exception('MAC validation failed');
        }

        try {

            DB::beginTransaction();

            // 先鎖住 避免同時更新
            $order = Order::query()
                ->with('items')
                ->where('order_key', $serverPost['MerchantTradeNo'])
                ->lockForUpdate()
                ->first();

            if (!$order) {
                throw new Exception('Order not found');
            }

            $existingPayload = $order->payload ? json_decode($order->payload, true) : [];
            $existingPayload[] = $serverPost;

            $ftCode = $serverPost['RtnCode'] ?? null;
            $order->update([
                'ft_rtn_code' => $ftCode,
                'payload' => $existingPayload,
            ]);

            DB::commit();

            $success = $ftCode === '1';
            $orderResource = new OrderResource($order);
            $orderArray = $orderResource->toArray(request());

            // 準備要提交的表單數據
            $formData = [
                'success' => $success,
                'order' => $orderArray,
            ];

            // View data with form submission data
            $viewData = [
                'targetUrl' => config('cs.order_result_url'),
                'formData' => $formData,
            ];

            return view('base-filament-plugin::payment.order-result', $viewData);

        } catch (\Throwable $e) {
            DB::rollBack();

            Log::error('ECPay notify processing error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }


    /**
     * 處理 ECPay ATM/CVS/BARCODE 取號結果通知 (PaymentInfoURL)
     *
     * @param Request $request
     * @return string
     * @throws JsonException
     */
    public function paymentInfoUrl(Request $request): string
    {
        $serverPost = $request->post();
        logger($serverPost);
        $checkMacValue = $request->post('CheckMacValue');
        // 移除 CheckMacValue 以進行驗證
        unset($serverPost['CheckMacValue']);
        // 產生檢查碼
        $checkCode = StringService::checkMacValueGenerator($serverPost);

        if ($checkMacValue !== $checkCode) {
            return '0|FAIL';
        }

        $paymentType = $request->post('PaymentType');
        $rtnCode = $request->post('RtnCode');
        $paymentSuccess = false;


        // 根據付款方式處理不同的取號結果
        $validAtmCodes = [
            'ATM_BOT',        // 台灣銀行
            'ATM_CHINATRUST', // 中國信託
            'ATM_FIRST',      // 第一銀行
            'ATM_LAND',       // 土地銀行
            'ATM_CATHAY',     // 國泰世華銀行
            'ATM_PANHSIN',    // 板信銀行
            'ATM_KGI',        // 凱基銀行
        ];

        $validCvsCodes = [
            'CVS_CVS',    // 超商代碼
            'CVS_OK',     // OK 超商
            'CVS_FAMILY', // 全家超商
            'CVS_HILIFE', // 萊爾富超商
            'CVS_IBON',   // 7-11 ibon
        ];

        $validBarcodeCodes = [
            'BARCODE_BARCODE', // 超商條碼
        ];

        if (in_array($paymentType, $validAtmCodes)) {
            // ATM 虛擬帳號取號
            $paymentSuccess = $rtnCode == 2;
        } elseif (in_array($paymentType, $validCvsCodes)) {
            // 超商代碼取號
            $paymentSuccess = $rtnCode == 10100073;
        } elseif (in_array($paymentType, $validBarcodeCodes)) {
            // 超商條碼取號
            $paymentSuccess = $rtnCode == 10100073;
        }


        // 更新訂單付款資訊
        $order = Order::where('order_key', $serverPost['MerchantTradeNo'])->first();
        if ($order) {
            $existingPayload = $order->payload ? json_decode($order->payload, true) : [];
            $newPayload = array_merge($existingPayload, [$serverPost]);

            $order->update([
                'payment_status' => $paymentSuccess ? ($order->payment_status) : EnumPaymentStatus::FAILED->value,
                'payload' => $newPayload,
                'bank_code' => $serverPost['BankCode'] ?? null,
                'v_account' => $serverPost['vAccount'] ?? null,
                'payment_no' => $serverPost['PaymentNo'] ?? null,
                'expire_date' => $serverPost['ExpireDate'] ?? null,
                'barcode1' => $serverPost['Barcode1'] ?? null,
                'barcode2' => $serverPost['Barcode2'] ?? null,
                'barcode3' => $serverPost['Barcode3'] ?? null,
            ]);
        }

        return '1|OK';
    }

    /**
     * 處理 ECPay 結帳請求
     *
     * @param Request $request
     * @return View
     */
    public function checkout(Request $request): View
    {
        try {
            // 從 POST 請求中取得 order_key
            $orderKey = $request->input('order_key');

            // 沒有提供 order_key 時回傳 404
            if (!$orderKey) {
                abort(404);
            }

            // 根據 order_key 取得訂單，不存在時回傳 404
            $order = Order::with('items')->where('order_key', $orderKey)->first();

            if (!$order) {
                abort(404);
            }

            // 從訂單中取得付款方式
            $paymentMethod = $order->payment_method;

            // 使用 PaymentService 建立付款訂單
            return $this->paymentService->checkout($order, $paymentMethod);
        } catch (Exception $e) {
            Log::error('ECPay 結帳失敗: ' . $e->getMessage());
            abort('404');
        }
    }



}
