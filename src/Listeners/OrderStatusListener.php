<?php

namespace Stephenchenorg\BaseFilamentPlugin\Listeners;

use Stephenchenorg\BaseFilamentPlugin\Events\OrderStatusChanged;
use Illuminate\Support\Facades\DB;
use Exception;

class OrderStatusListener
{
    /**
     * Handle the event.
     */
    public function handle(OrderStatusChanged $event): void
    {
        $order = $event->order;

        try {
            DB::beginTransaction();

            // 目前沒有特定的 side effect 需要處理
            // 可以在這裡添加訂單狀態變更時需要執行的邏輯
            // 例如：發送通知、記錄日誌等

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("處理訂單狀態變更失敗: {$e->getMessage()}");
        }
    }
}
