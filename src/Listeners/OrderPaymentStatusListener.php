<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\OrderPaymentStatusChanged;
use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Illuminate\Support\Facades\DB;
use Exception;

class OrderPaymentStatusListener
{
    /**
     * Handle the event.
     */
    public function handle(OrderPaymentStatusChanged $event): void
    {
        $order = $event->order;

        try {
            DB::beginTransaction();

            // 當付款狀態為已付款且配送狀態為已完成時，將訂單狀態改為完成
            if ($order->payment_status === EnumPaymentStatus::PAID->value &&
                $order->shipping_status === EnumShippingStatus::COMPLETED->value
            ) {
                $order->status()->transitionTo(EnumOrderStatus::COMPLETED->value);
            }

            // 當付款狀態為失敗時，將訂單狀態改為失敗
            if ($order->payment_status === EnumPaymentStatus::FAILED->value) {
                $order->status()->transitionTo(EnumOrderStatus::FAILED->value);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("處理付款狀態變更失敗: {$e->getMessage()}");
        }
    }
}
