<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 訂單項目列表 DTO
 */
class OrderItemListDTO
{
    /**
     * @param OrderItemDTO[] $items
     */
    public function __construct(
        public readonly array $items
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            items: OrderItemDTO::fromArrayCollection($data)
        );
    }

    /**
     * 轉換為陣列
     */
    public function toArray(): array
    {
        return OrderItemDTO::toArrayCollection($this->items);
    }

    /**
     * 從陣列集合創建 DTO 集合
     * 
     * @param array $dataCollection
     * @return OrderItemListDTO[]
     */
    public static function fromArrayCollection(array $dataCollection): array
    {
        return array_map(fn($data) => self::fromArray($data), $dataCollection);
    }

    /**
     * 將 DTO 集合轉換為陣列集合
     * 
     * @param OrderItemListDTO[] $dtos
     * @return array
     */
    public static function toArrayCollection(array $dtos): array
    {
        return array_map(fn($dto) => $dto->toArray(), $dtos);
    }
}
