<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitEditRecord;

class EditRewardRecord extends EditRecord
{
    use CCTraitEditRecord;

    protected static string $resource = RewardRecordResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
