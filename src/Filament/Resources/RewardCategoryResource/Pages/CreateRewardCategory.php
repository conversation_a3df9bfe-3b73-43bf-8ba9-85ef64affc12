<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource;
use Filament\Resources\Pages\CreateRecord;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitCreateRecord;

class CreateRewardCategory extends CreateRecord
{
    use CCTraitCreateRecord;

    protected static string $resource = RewardCategoryResource::class;
}
