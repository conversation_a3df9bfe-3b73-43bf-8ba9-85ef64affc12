<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\RelationManagers;

use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;

class SpecificationsRelationManager extends RelationManager
{
    use CCTraitColumn;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitAction;

    protected static string $relationship = 'specifications';

    protected static ?string $title = '價格與型號表';

    protected static ?string $label = '價格與型號表';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                ...$this->getAttributeForm(),

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([


                        self::getFormToggle('status')
                            ->label('啟用/停用'),

                        TextInput::make('sku')
                            ->label('SKU')
                            ->required()
                            ->maxLength(64),

                        TextInput::make('ean')
                            ->label(' EAN 條碼')
                            ->nullable()
                            ->maxLength(15),


                        //                        TextInput::make('type')
                        //                            ->label('類型')
                        //                            ->required()
                        //                            ->maxLength(20),

                        Hidden::make('type')
                            ->default('none'),


                        self::getFormSort(),

                    ]),


                Section::make('data_settings')
                    ->id('data_settings')
                    ->heading('數據設定')
                    ->schema([

                        TextInput::make('listing_price')
                            ->label('定價')
                            ->numeric() // 確保是數字
                            ->minValue(0)
                            ->required(),


                        TextInput::make('selling_price')
                            ->label('售價')
                            ->numeric() // 確保是數字
                            ->minValue(0)
                            ->required(),

                        TextInput::make('inventory')
                            ->label('庫存')
                            ->numeric() // 確保是數字
                            ->minValue(0)
                            ->default(0)
                            ->required(),

                    ]),

                self::getTabLanguage([
                    self::getFormTitle()->nullable(),
                    self::getFormEditor('content')
                        ->label('內容 - 圖文編輯器')
                        ->placeholder('請輸入內容')
                        ->columnSpan('full'),
                ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('sku')
            ->columns([

                TextColumn::make('sku')
                    ->label('sku'),

                TextColumn::make('inventory')
                    ->label('庫存'),

                TextColumn::make('combination_key')
                    ->label('規格組合')
                    ->formatStateUsing(function ($state, $record) {
                        $serviceProductSpecification = app(ServiceProductSpecification::class);

                        if (empty($state)) {
                            $name = '';
                        } else {
                            $name = $serviceProductSpecification->getCombinationName($state);
                        }

                        if (!$serviceProductSpecification->validateCombinationKey($state, $record->product)) {
                            $name = $name . "&nbsp;&nbsp;" . '<span style="color: red;">請更新</span>';
                        }

                        return new HtmlString($name);
                    }),

                TextColumn::make('listing_price')
                    ->label('建議售價')
                    ->abbr('B2C 官網的建議售價', asTooltip: true),

                TextColumn::make('selling_price')
                    ->label('銷售價一')
                    ->abbr('B2C 官網的銷售價', asTooltip: true)

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->using(function (array $data, string $model, $action): Model {

                        $combinationKey = $data['combination_key'] ?? null;

                        $exist = ProductSpecification::query()
                            ->where('product_id', '=', $this->getOwnerRecord()->id)
                            ->where('combination_key', '=', $combinationKey)
                            ->exists();

                        if ($exist) {
                            Notification::make()
                                ->title('已經存在相同規格')
                                ->color('danger')
                                ->danger()
                                ->send();
                            $action->halt();
                        }
                        return $model::create([
                            ...$data,
                            'product_id' => $this->getOwnerRecord()->id,
                        ]);
                    })
            ])
            ->actions([
                self::getActionEdit()
                    ->using(function (Model $record, array $data, $action): Model {

                        $combinationKey = $data['combination_key'] ?? null;

                        $exist = ProductSpecification::query()
                            ->whereNot('id', '=', $record->id)
                            ->where('product_id', '=', $this->getOwnerRecord()->id)
                            ->where('combination_key', '=', $combinationKey)
                            ->exists();

                        if ($exist) {
                            Notification::make()
                                ->title('已經存在相同規格')
                                ->color('danger')
                                ->danger()
                                ->send();
                            $action->halt();
                        }

                        $record->update($data);

                        return $record;
                    }),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * @return array
     */
    public function getAttributeForm(): array
    {
        $ownerRecord = $this->getOwnerRecord();

        $form = [];

        foreach ($ownerRecord->attributes as $attribute) {

            $attributeTitle = $attribute->translations()
                ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                ->first()->title;

            $options = $attribute->items->mapWithKeys(function ($item) {
                $optionTitle = $item->translations()->where('lang', '=', ServiceLanguage::getDefaultLanguage())->first()->title;
                return [$item->id => $optionTitle];
            })->toArray();

            $form[] = Select::make($this->getAttributeKey($attribute->id))
                ->label($attributeTitle)
                ->options($options)
                ->required()
                ->formatStateUsing(function (Get $get) use ($options) {
                    return $this->getSelectedOption($options, $get);
                })
                ->dehydrated(false);
        }

        $form[] = Hidden::make('combination_key')
            ->dehydrateStateUsing(function (Get $get) {
                return $this->getCombinationKey($get);
            });

        return [
            Section::make('attribute_settings')
                ->visible(!$ownerRecord->attributes->isEmpty())
                ->dehydrated()
                ->heading('規格屬性設定')
                ->schema($form)
        ];

    }


    /**
     * @param Get $get
     * @return String|null
     * @throws ValidationException
     */
    public function getCombinationKey(Get $get): ?string
    {
        $ownerRecord = $this->getOwnerRecord();

        if ($ownerRecord->attributes->isEmpty()) return null;

        $attributeIds = $ownerRecord->attributes->map(function ($attribute) {
            return $attribute->id;
        })->toArray();

        $selectedItemIds = array_map(function ($attributeId) use ($get) {
            return $get($this->getAttributeKey($attributeId));
        }, $attributeIds);

        sort($selectedItemIds);

        $combinationKey = implode("-", $selectedItemIds);

        $exist = ProductSpecification::query()
            ->where('combination_key', $combinationKey)
            ->whereNot('id', '=', $get('id') ?? null)
            ->exists();

        if ($exist) {

            Notification::make()
                ->title('規格組合已存在')
                ->body('請更新規格屬性的選單')
                ->danger()
                ->send();

            throw ValidationException::withMessages([
                'combination_key' => '該規格組合已經存在',
            ]);
        }

        return implode("-", $selectedItemIds);
    }

    /**
     * @param int $id
     * @return string
     */
    public function getAttributeKey(int $id): string
    {
        return "attribute_{$id}";
    }

    /**
     * 根據 combination_key 還原 Select 的選項
     *
     * @param array $options
     * @param Get $get
     * @return int|null
     */
    public function getSelectedOption(array $options, Get $get): ?int
    {
        $combinationKey = $get('combination_key');

        if (!$combinationKey) return null;

        $selectedItemIds = explode("-", $combinationKey);

        $optionIds = array_keys($options);

        $selectedOptionId = array_intersect($selectedItemIds, $optionIds);

        if (empty($selectedOptionId)) return null;

        return array_shift($selectedOptionId);
    }


}
