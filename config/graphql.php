<?php

declare(strict_types=1);

use App\GraphQL\Mutations\Client\ClientLoginMutation;
use App\GraphQL\Mutations\Client\ClientMeUpdateMutation;
use App\GraphQL\Mutations\Client\ClientOrderMutation;
use App\GraphQL\Mutations\Customer\CustomerMeUpdateMutation;
use App\GraphQL\Mutations\Customer\CustomerOrderMutation;
use App\GraphQL\Mutations\Customer\CustomerRegisterMutation;
use App\GraphQL\Types\ClientType;
use App\GraphQL\Types\CustomerType;
use App\GraphQL\Types\Order\CartCalculationResultType;
use App\GraphQL\Types\Order\CartValidationResultType;
use App\GraphQL\Types\Order\OrderAmountType;
use App\GraphQL\Types\Order\OrderItemInputType;
use App\GraphQL\Types\Order\OrderItemType;
use App\GraphQL\Types\Order\OrderType;
use App\GraphQL\Types\ProductSpecificationType;
use App\GraphQL\Types\ProductType;
use Rebing\GraphQL\Support\UploadType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\ErrorFormatter;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\ErrorHandler;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Client\ClientForgetPasswordMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Client\ClientLogoutMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Client\ClientReadyToForgetMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Client\ClientRefreshMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Client\ClientRegisterMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Client\ClientResetPasswordMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Client\ClientValidateEmailMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\CreateContactMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerForgetPasswordMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerLoginMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerLogoutMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerReadyToForgetMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerRefreshMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerResetPasswordMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Mutations\Customer\CustomerValidateEmailMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Article\ArticleCategoriesQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Article\ArticleCategoryQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Article\ArticleQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Article\ArticlesQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Banner\BannerQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Banner\BannersQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Brand\BrandQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Brand\BrandsQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Client\ClientMeQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Company\CompanySettingQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Customer\CustomerMeQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Faq\FaqCategoriesQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Faq\FaqCategoryQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Faq\FaqQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Faq\FaqsQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Order\OrderQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Order\OrdersQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Page\PageQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Page\PagesQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product\ProductCategoriesQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product\ProductCategoryQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Tag\TagQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Tag\TagsQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Article\ArticleCategoryTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Article\ArticleCategoryType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Article\ArticleType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Auth\ClientLoginType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Auth\CustomerLoginType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Auth\TokenType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Banner\BannerType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Brand\BrandTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Brand\BrandType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Company\CompanySettingType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Faq\FaqCategoryTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Faq\FaqCategoryType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Faq\FaqTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Faq\FaqType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Image\BackgroundType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Image\CoverType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Image\ImageType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Page\PageFieldTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Page\PageFieldType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Page\PageTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Page\PageType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductAttributeItemTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductAttributeItemType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductAttributeTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductAttributeType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductCategoryTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductCategoryType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductDetailTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductDetailType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductImageType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductSpecificationTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product\ProductTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Tag\TagTranslationType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Tag\TagType;
use Stephenchenorg\BaseFilamentPlugin\Middleware\LanguageMiddleware;


return [

    'route' => [
        // The prefix for routes; do NOT use a leading slash!
        'prefix' => 'graphql',

        // The controller/method to use in GraphQL request.
        // Also supported array syntax: `[\Rebing\GraphQL\GraphQLController::class, 'query']`
        'controller' => Rebing\GraphQL\GraphQLController::class . '@query',

        // Any middleware for the graphql route group
        // This middleware will apply to all schemas
        'middleware' => [LanguageMiddleware::class],

        // Additional route group attributes
        //
        // Example:
        //
        // 'group_attributes' => ['guard' => 'api']
        //
        'group_attributes' => [],
    ],

    // The name of the default schema
    // Used when the route group is directly accessed
    'default_schema' => 'default',

    'batching' => [
        // Whether to support GraphQL batching or not.
        // See e.g. https://www.apollographql.com/blog/batching-client-graphql-queries-a685f5bcd41b/
        // for pro and con
        'enable' => true,
    ],

    // The schemas for query and/or mutation. It expects an array of schemas to provide
    // both the 'query' fields and the 'mutation' fields.
    //
    // You can also provide a middleware that will only apply to the given schema
    //
    // Example:
    //
    //  'schemas' => [
    //      'default' => [
    //          'controller' => MyController::class . '@method',
    //          'query' => [
    //              Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\UsersQuery::class,
    //          ],
    //          'mutation' => [
    //
    //          ]
    //      ],
    //      'user' => [
    //          'query' => [
    //              Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\ProfileQuery::class,
    //          ],
    //          'mutation' => [
    //
    //          ],
    //          'middleware' => ['auth'],
    //      ],
    //      'user/me' => [
    //          'query' => [
    //              Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\MyProfileQuery::class,
    //          ],
    //          'mutation' => [
    //
    //          ],
    //          'middleware' => ['auth'],
    //      ],
    //  ]
    //
    'schemas' => [
        'default' => [
            'query' => [

                'article' => ArticleQuery::class,
                'articles' => ArticlesQuery::class,
                'articleCategory' => ArticleCategoryQuery::class,
                'articleCategories' => ArticleCategoriesQuery::class,

                'faq' => FaqQuery::class,
                'faqs' => FaqsQuery::class,
                'faqCategory' => FaqCategoryQuery::class,
                'faqCategories' => FaqCategoriesQuery::class,
                'banner' => BannerQuery::class,
                'banners' => BannersQuery::class,

                'page' => PageQuery::class,
                'pages' => PagesQuery::class,
                'product' => App\GraphQL\Queries\ProductQuery::class,
                'products' => App\GraphQL\Queries\ProductsQuery::class,
                'productSpecification' => App\GraphQL\Queries\ProductSpecificationQuery::class,
                'productSpecifications' => App\GraphQL\Queries\ProductSpecificationsQuery::class,
                'productCategory' => ProductCategoryQuery::class,
                'productCategories' => ProductCategoriesQuery::class,

                'companySetting' => CompanySettingQuery::class,

                'tag' => TagQuery::class,
                'tags' => TagsQuery::class,


                'brand' => BrandQuery::class,
                'brands' => BrandsQuery::class,

                // Customer and Client queries
                'customerMe' => CustomerMeQuery::class,
                'clientMe' => ClientMeQuery::class,

                // Order queries
                'order' => OrderQuery::class,
                'orders' => OrdersQuery::class,

                // Cart queries
                'calculateCart' => App\GraphQL\Queries\CalculateCartQuery::class,
                'validateCart' => App\GraphQL\Queries\ValidateCartQuery::class,

            ],
            'mutation' => [
                'contactUs' => CreateContactMutation::class,
                'customerLogin' => CustomerLoginMutation::class,
                'customerValidateEmail' => CustomerValidateEmailMutation::class,
                'customerRegister' => CustomerRegisterMutation::class,
                'customerLogout' => CustomerLogoutMutation::class,
                'customerRefresh' => CustomerRefreshMutation::class,
                'customerResetPassword' => CustomerResetPasswordMutation::class,
                'customerReadyToForget' => CustomerReadyToForgetMutation::class,
                'customerForgotPassword' => CustomerForgetPasswordMutation::class,
                'customerMeUpdate' => CustomerMeUpdateMutation::class,
                'customerOrder' => CustomerOrderMutation::class,

                // Client mutations
                'clientLogin' => ClientLoginMutation::class,
                'clientValidateEmail' => ClientValidateEmailMutation::class,
                'clientRegister' => ClientRegisterMutation::class,
                'clientLogout' => ClientLogoutMutation::class,
                'clientRefresh' => ClientRefreshMutation::class,
                'clientResetPassword' => ClientResetPasswordMutation::class,
                'clientReadyToForget' => ClientReadyToForgetMutation::class,
                'clientForgotPassword' => ClientForgetPasswordMutation::class,
                'clientMeUpdate' => ClientMeUpdateMutation::class,
                'clientOrder' => ClientOrderMutation::class,
            ],
            // The types only available in this schema
            'types' => [
                UploadType::class,
            ],

            // Laravel HTTP middleware
            'middleware' => null,

            // Which HTTP methods to support; must be given in UPPERCASE!
            'method' => ['GET', 'POST'],

            // An array of middlewares, overrides the global ones
            'execution_middleware' => null,
        ],
    ],

    // The global types available to all schemas.
    // You can then access it from the facade like this: GraphQL::type('user')
    //
    // Example:
    //
    // 'types' => [
    //     Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\UserType::class
    // ]
    //


    'types' => [
        'Article' => ArticleType::class,
        'ArticleCategory' => ArticleCategoryType::class,
        'ArticleCategoryTranslation' => ArticleCategoryTranslationType::class,
        'Faq' => FaqType::class,
        'FaqTranslation' => FaqTranslationType::class,
        'FaqCategory' => FaqCategoryType::class,
        'FaqCategoryTranslation' => FaqCategoryTranslationType::class,
        'Banner' => BannerType::class,
        'Page' => PageType::class,
        'PageTranslation' => PageTranslationType::class,
        'PageField' => PageFieldType::class,
        'PageFieldTranslation' => PageFieldTranslationType::class,
        'Product' => ProductType::class,
        'ProductTranslation' => ProductTranslationType::class,
        'ProductCategory' => ProductCategoryType::class,
        'ProductCategoryTranslation' => ProductCategoryTranslationType::class,
        'ProductImage' => ProductImageType::class,
        'ProductAttribute' => ProductAttributeType::class,
        'ProductAttributeTranslation' => ProductAttributeTranslationType::class,
        'ProductAttributeItem' => ProductAttributeItemType::class,
        'ProductAttributeItemTranslation' => ProductAttributeItemTranslationType::class,
        'ProductSpecification' => ProductSpecificationType::class,
        'ProductSpecificationTranslation' => ProductSpecificationTranslationType::class,
        'CompanySetting' => CompanySettingType::class,
        'Tag' => TagType::class,
        'TagTranslation' => TagTranslationType::class,
        'Brand' => BrandType::class,
        'BrandTranslation' => BrandTranslationType::class,
        'ProductDetail' => ProductDetailType::class,
        'ProductDetailTranslation' => ProductDetailTranslationType::class,
        'Image' => ImageType::class,
        'Background' => BackgroundType::class,
        'Cover' => CoverType::class,
        'Customer' => CustomerType::class,
        'CustomerLogin' => CustomerLoginType::class,
        'Token' => TokenType::class,
        'Client' => ClientType::class,
        'ClientLogin' => ClientLoginType::class,
        'Order' => OrderType::class,
        'OrderItem' => OrderItemType::class,
        'OrderItemInput' => OrderItemInputType::class,
        'CartValidationResult' => CartValidationResultType::class,
        'OrderAmount' => OrderAmountType::class,

        //         ExampleType::class,
        // ExampleRelationType::class,
        //         \Rebing\GraphQL\Support\UploadType::class,
    ],

    // This callable will be passed the Error object for each errors GraphQL catch.
    // The method should return an array representing the error.
    // Typically:
    // [
    //     'message' => '',
    //     'locations' => []
    // ]
    'error_formatter' => [ErrorFormatter::class, 'formatError'],

    /*
     * Custom Error Handling
     *
     * Expected handler signature is: function (array $errors, callable $formatter): array
     *
     * The default handler will pass exceptions to laravel Error Handling mechanism
     */
    'errors_handler' => [ErrorHandler::class, 'handleErrors'],

    /*
     * Options to limit the query complexity and depth. See the doc
     * @ https://webonyx.github.io/graphql-php/security
     * for details. Disabled by default.
     */
    'security' => [
        'query_max_complexity' => null,
        'query_max_depth' => null,
        'disable_introspection' => false,
    ],

    /*
     * You can define your own pagination type.
     * Reference \Rebing\GraphQL\Support\PaginationType::class
     */
    'pagination_type' => Rebing\GraphQL\Support\PaginationType::class,

    /*
     * You can define your own simple pagination type.
     * Reference \Rebing\GraphQL\Support\SimplePaginationType::class
     */
    'simple_pagination_type' => Rebing\GraphQL\Support\SimplePaginationType::class,

    /*
     * Overrides the default field resolver
     * See http://webonyx.github.io/graphql-php/data-fetching/#default-field-resolver
     *
     * Example:
     *
     * ```php
     * 'defaultFieldResolver' => function ($root, $args, $context, $info) {
     * },
     * ```
     * or
     * ```php
     * 'defaultFieldResolver' => [SomeKlass::class, 'someMethod'],
     * ```
     */
    'defaultFieldResolver' => null,

    /*
     * Any headers that will be added to the response returned by the default controller
     */
    'headers' => [],

    /*
     * Any JSON encoding options when returning a response from the default controller
     * See http://php.net/manual/function.json-encode.php for the full list of options
     */
    'json_encoding_options' => 0,

    /*
     * Automatic Persisted Queries (APQ)
     * See https://www.apollographql.com/docs/apollo-server/performance/apq/
     *
     * Note 1: this requires the `AutomaticPersistedQueriesMiddleware` being enabled
     *
     * Note 2: even if APQ is disabled per configuration and, according to the "APQ specs" (see above),
     *         to return a correct response in case it's not enabled, the middleware needs to be active.
     *         Of course if you know you do not have a need for APQ, feel free to remove the middleware completely.
     */
    'apq' => [
        // Enable/Disable APQ - See https://www.apollographql.com/docs/apollo-server/performance/apq/#disabling-apq
        'enable' => env('GRAPHQL_APQ_ENABLE', false),

        // The cache driver used for APQ
        'cache_driver' => env('GRAPHQL_APQ_CACHE_DRIVER', config('cache.default')),

        // The cache prefix
        'cache_prefix' => config('cache.prefix') . ':graphql.apq',

        // The cache ttl in seconds - See https://www.apollographql.com/docs/apollo-server/performance/apq/#adjusting-cache-time-to-live-ttl
        'cache_ttl' => 300,
    ],

    /*
     * Execution middlewares
     */
    'execution_middleware' => [
        Rebing\GraphQL\Support\ExecutionMiddleware\ValidateOperationParamsMiddleware::class,
        // AutomaticPersistedQueriesMiddleware listed even if APQ is disabled, see the docs for the `'apq'` configuration
        Rebing\GraphQL\Support\ExecutionMiddleware\AutomaticPersistedQueriesMiddleware::class,
        Rebing\GraphQL\Support\ExecutionMiddleware\AddAuthUserContextValueMiddleware::class,
        // \Rebing\GraphQL\Support\ExecutionMiddleware\UnusedVariablesMiddleware::class,
    ],

    /*
     * Globally registered ResolverMiddleware
     */
    'resolver_middleware_append' => null,

    'context' => [],
];
