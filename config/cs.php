<?php

use App\Filament\Resources\ArticleCategoryResource;
use App\Filament\Resources\ArticleResource;
use App\Filament\Resources\TagResource;

return [

    // 是否可見
    'article_visible'                => env('CS_ARTICLE_VISIBLE', true),
    'banner_visible'                 => env('CS_BANNER_VISIBLE', true),
    'contact_visible'                => env('CS_CONTACT_VISIBLE', true),
    'faq_visible'                    => env('CS_FAQ_VISIBLE', true),
    'product_visible'                => env('CS_PRODUCT_VISIBLE', true),
    'brand_visible'                  => env('CS_BRAND_VISIBLE', true),
    'customer_visible'               => env('CS_CUSTOMER_VISIBLE', true),
    'client_visible'                 => env('CS_CLIENT_VISIBLE', true),

    // 控制熱門與最新的數量
    'article_hottest_limit'          => env('CS_ARTICLE_HOTTEST_LIMIT', 6),
    'product_hottest_limit'          => env('CS_PRODUCT_HOTTEST_LIMIT', 3),
    'product_category_hottest_limit' => env('CS_PRODUCT_CATEGORY_HOTTEST_LIMIT', 6),
    'article_newest_limit'          => env('CS_ARTICLE_NEWEST_LIMIT', 6),
    'product_newest_limit'          => env('CS_PRODUCT_NEWEST_LIMIT', 3),
    'product_category_newest_limit' => env('CS_PRODUCT_CATEGORY_NEWEST_LIMIT', 6),

    // 是否讓作品集與產品共享標籤
    'share_tag'                      => true,
    // 文章名稱
    'article_name'                   => env('CS_ARTICLE_NAME', '作品集'),
    // 產品有哪些 type
    'product_types'                  => ['product','service'],
    // 是否自動產生壓縮後的圖片
    'image_variants'                 => env('CS_IMAGE_VARIANTS', true),
    // 信件中所顯示的店家名稱
    'mail_name'                      => '預設店家',
    // 控制後台有哪些語系
    'languages'                      => [ 'zh_TW', 'en'],

    // 模型觀察者設定
    'observer_class'                 => \Stephenchenorg\BaseFilamentPlugin\Observers\AdminFieldsObserver::class,

    // 額外需要觀察的模型
    'additional_observable_models'   => [
        [
            'path' => 'app/Models',
            'namespace' => 'App\\Models\\',
        ],
    ],

    'admin' => [
        // 系統管理員最高權限帳號
        'app_hidden_super_admin_account' => env('CS_HIDDEN_SUPER_ADMIN_ACCOUNT', ''),
        'app_hidden_super_admin_password' => env('CS_HIDDEN_SUPER_ADMIN_PASSWORD', ''),

        // 使用者最高權限帳號
        'app_super_admin_account' => env('CS_SUPER_ADMIN_ACCOUNT', ''),
        'app_super_admin_password' => env('CS_SUPER_ADMIN_PASSWORD', ''),
    ],

    'customer' => [
        'app_super_customer_account' => env('CS_SUPER_CUSTOMER_ACCOUNT', ''),
        'app_super_customer_password' => env('CS_SUPER_CUSTOMER_PASSWORD', ''),
    ],
    'client' => [
        'app_super_client_account' => env('CS_SUPER_CLIENT_ACCOUNT', ''),
        'app_super_client_password' => env('CS_SUPER_CLIENT_PASSWORD', ''),
    ],

    // 模型配置
    'models' => [
        'ClientModel' => \Stephenchenorg\BaseFilamentPlugin\Models\Client::class,
        'CustomerModel' => \Stephenchenorg\BaseFilamentPlugin\Models\Customer::class,
    ],



    // 可以覆蓋或修改這個數組來控制要註冊的 Resources
    'resources'                      => [
        'ActivityLog' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource::class,
        'Admin' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\AdminResource::class,
        'Article' => ArticleResource::class,
        'ArticleCategory' => ArticleCategoryResource::class,
        'Banner' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource::class,
        'Brand' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource::class,
        'Client' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource::class,
        'Contact' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource::class,
        'Customer' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource::class,
        'Faq' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqResource::class,
        'FaqCategory' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource::class,
        'Page' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource::class,
        'Permission' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource::class,
        'Product' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource::class,
        'ProductCategory' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource::class,
        'ProductSpecification' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource::class,
        'Role' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource::class,
        'Tag' => TagResource::class,
        'Order' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource::class,
    ],

    // 可以覆蓋或修改這個數組來控制要執行的 Seeders
    'seeders' => [
        'Permission' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\PermissionSeeder::class,
        'Role' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\RoleSeeder::class,
        'Cities' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CitiesSeeder::class,
        'Zone' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ZoneSeeder::class,
        'Admin' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\AdminSeeder::class,
        'Customer' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CustomerSeeder::class,
        'Client' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ClientSeeder::class,
        'Tag' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\TagSeeder::class,
        'Article' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ArticleSeeder::class,
        'Banner' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\BannerSeeder::class,
        'Company' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CompanySeeder::class,
        'Contact' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ContactSeeder::class,
        'Faq' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\FaqSeeder::class,
        'Page' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\PageSeeder::class,
        'Product' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ProductSeeder::class,
        'System' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\SystemSeeder::class,
        'Order' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\OrderSeeder::class,
        'ShippingMethod' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ShippingMethodSeeder::class,
    ],

    // 可以覆蓋或修改這個數組來控制要註冊的 Pages
    'pages' => [
        'CompanySetting' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\CompanySettingPage::class,
        'SystemSetting' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\SystemSettingPage::class,
        'MyPage' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\MyPage::class,
        'ShippingMethodSetting' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\ShippingMethodSettingPage::class,
    ],

    // 可以覆蓋或修改這個數組來控制要註冊的 Widgets
    'widgets' => [
        'Article' => \Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ArticleWidget::class,
        'Contact' => \Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ContactWidget::class,
        'Product' => \Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ProductWidget::class,
    ],


    // 服務介面實作綁定
    'implements' => [
        \Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceProductSpecificationInterface::class => [
            'implementation' => \Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductSpecification::class,
            'binding_type' => 'singleton', // singleton 或 bind
        ],
        \Stephenchenorg\BaseFilamentPlugin\Contracts\OrderServiceInterface::class => [
            'implementation' => \Stephenchenorg\BaseFilamentPlugin\Service\ServiceOrder::class,
            'binding_type' => 'singleton', // singleton 或 bind
        ],
        \Stephenchenorg\BaseFilamentPlugin\Contracts\PaymentServiceInterface::class => [
            'implementation' => \Stephenchenorg\BaseFilamentPlugin\Service\Gateway\ECPay\ServiceECPay::class,
            'binding_type' => 'singleton', // singleton 或 bind
        ],
    ],


    // 控制金流平台
    'payment_gateway'                => env('CS_PAYMENT_GATEWAY', 'ecpay'),
    // 接受的付款方式
    'accept_payment_methods'         => explode(',', env('CS_ACCEPT_PAYMENT_METHODS', 'NONE,ALL,CREDIT_CARD,ATM,WEB_ATM,CVS,BARCODE')),
    // 接受的發票方式
    'accept_invoice_methods'         => explode(',', env('CS_ACCEPT_INVOICE_METHODS', 'none,paper,mobile_barcode,citizen_card')),
    // 接受的運送方式
    'accept_shipping_methods'        => explode(',', env('CS_ACCEPT_SHIPPING_METHODS', 'none,delivery,ok_mart,seven_eleven,family_mart,hi_life')),
    // 三聯單時 是否外加營業稅
    'triplicate_tax'                 => env('CS_TRIPLICATE_TAX', true),
    // 金流模式
    'payment_mode'                => env('CS_PAYMENT_MODE', 'development'),
    // 付款成功轉回的前端網址
    'order_result_url'            => env('CS_ORDER_RESULT_URL'),
    // 是否使用預設的金流路由
    'use_default_payment_routes' => env('CS_USE_DEFAULT_PAYMENT_ROUTES', true),

];
