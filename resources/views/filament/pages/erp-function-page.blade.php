<x-filament-panels::page>
    @vite('resources/js/mermaid.js')
    <div class="mermaid">
        {!! $graphData !!}
    </div>

    <div class="mt-6">

    </div>

    {{ $this->form }}

    <!-- 更新說明 -->
    <div class="bg-white p-6 rounded-md shadow-md space-y-2">
        <p class="text-gray-700 font-semibold flex items-center">
            <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3M12 2a10 10 0 100 20 10 10 0 000-20z" />
            </svg>
            每<span class="font-bold text-blue-600 mx-1">小時</span> 更新產品資料
        </p>
        <div class="text-gray-600">
            <p class="mb-1 font-medium">更新 API 內容：</p>
            <ul class="list-disc list-inside space-y-1">
                <li>產品</li>
                <li>產品庫存</li>
                <li><span style="color: red;">經由討論後確定產品類別不同步ERP</span></li>
                <li><span style="color: red;">系統不會更新結尾為 '-000'、'-900'、'-998' 的產品型號</span></li>
            </ul>
        </div>
    </div>

    <!-- 產品 -->
    <div class="bg-white p-6 rounded-md shadow-md">
        <h2 class="text-base font-bold text-gray-800 mb-3 border-b pb-1">📦 產品資訊</h2>
        <table class="w-full table-auto border rounded overflow-hidden shadow-sm">
            <tbody class="divide-y divide-gray-100">
                <tr>
                    <td class="p-3 font-semibold text-gray-700 w-40">名稱</td>
                    <td class="p-3 text-gray-800 border-l">INAME</td>
                </tr>

                <tr>
                    <td class="p-3 font-semibold text-gray-700">新品標示</td>
                    <td class="p-3 text-gray-800 border-l">ITNEW</td>
                </tr>
                <tr>
                    <td class="p-3 font-semibold text-gray-700">主力商品</td>
                    <td class="p-3 text-gray-800 border-l">IMAIN</td>
                </tr>
                <tr>
                    <td class="p-3 font-semibold text-gray-700">內容一</td>
                    <td class="p-3 text-gray-800 border-l">T11_6</td>
                </tr>
                <tr>
                    <td class="p-3 font-semibold text-gray-700">內容二</td>
                    <td class="p-3 text-gray-800 border-l">T11_7</td>
                </tr>
                <tr>
                    <td class="p-3 font-semibold text-gray-700">內容三</td>
                    <td class="p-3 text-gray-800 border-l">T11_8</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 產品規格 -->
    <div class="bg-white p-6 rounded-md shadow-md">
        <h2 class="text-base font-bold text-gray-800 mb-3 border-b pb-1">📋 產品規格</h2>
        <table class="w-full table-auto border rounded overflow-hidden shadow-sm">
            <tbody class="divide-y divide-gray-100">
            <tr>
                <td class="p-3 font-semibold text-gray-700 w-40">名稱</td>
                <td class="p-3 text-gray-800 border-l">INAME</td>
            </tr>
            <tr>
                <td class="p-3 font-semibold text-gray-700">列表價</td>
                <td class="p-3 text-gray-800 border-l">IUNIT6</td>
            </tr>
            <tr>
                <td class="p-3 font-semibold text-gray-700">售價</td>
                <td class="p-3 text-gray-800 border-l">IUNIT</td>
            </tr>
            <tr>
                <td class="p-3 font-semibold text-gray-700">EAN條碼</td>
                <td class="p-3 text-gray-800 border-l">EAN13</td>
            </tr>
            <tr>
                <td class="p-3 font-semibold text-gray-700">庫存數量</td>
                <td class="p-3 text-gray-800 border-l">Num</td>
            </tr>
            <tr>
                <td class="p-3 font-semibold text-gray-700">屬性一</td>
                <td class="p-3 text-gray-800 border-l">ATTR1</td>
            </tr>
            <tr>
                <td class="p-3 font-semibold text-gray-700">屬性二</td>
                <td class="p-3 text-gray-800 border-l">ATTR2</td>
            </tr>
            </tbody>
        </table>
    </div>
</x-filament-panels::page>
