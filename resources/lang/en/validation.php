<?php

return [
    'required' => ':attribute is required.',
    'string' => ':attribute must be a string.',
    'nullable' => ':attribute is optional.',
    'email' => ':attribute format is invalid.',
    'max' => [
        'string' => ':attribute may not be greater than :max characters.'
    ],
    'min' => [
        'string' => ':attribute must be at least :min characters.'
    ],
    'regex' => [
        'password' => ':attribute must contain letters, numbers, and at least one special character.'
    ],
    'confirmed' => 'The :attribute confirmation does not match.',

    'custom' => [
        'file' => [
            'mimetypes' => 'The :attribute format must be one of the following: :values.',
            'max' => 'The size of :attribute may not exceed :max KB.',
        ],
        'file_path' => [
            'exists' => ':attribute does not exist.',
        ],
        'files' => [
            'array' => ':attribute must be an array.',
            'max' => ':attribute may not have more than :max items.',
        ],
        'password' => [
            'regex' => 'The password must be 8-20 characters long and contain letters, numbers, and at least one special character.',
        ],
    ],

    'attributes' => [
        'name' => 'name',
        'phone' => 'phone',
        'email' => 'email',
        'line' => 'line',
        'address' => 'address',
        'title' => 'title',
        'content' => 'content',
        'file' => 'attachments',
        'file_path' => 'file path',
        'password' => 'password',
        'password_confirmation' => 'password confirmation',
        'old_password' => 'old password',
    ],
];
