<?php

return [

    // General validation messages; :attribute will be replaced with the field name defined in 'attributes'
    'required' => ':attribute is required',
    'string' => ':attribute must be a string',
    'nullable' => ':attribute is optional',
    'email' => ':attribute format is invalid',
    'max' => [
        'string' => ':attribute may not be greater than :max characters',
        'numeric' => ':attribute may not be greater than :max',
        'file' => ':attribute may not be greater than :max KB',
        'array' => ':attribute may not have more than :max items',
    ],
    'min' => [
        'string' => ':attribute must be at least :min characters',
        'numeric' => ':attribute must be at least :min',
        'array' => ':attribute must have at least :min items',
    ],
    'integer' => ':attribute must be an integer',
    'exists' => 'The selected :attribute is invalid',
    'in' => 'The :attribute must be one of the following options  :『 :values 』',
    'array' => ':attribute must be an array',
    'regex' => [
        'password' => ':attribute must contain letters, numbers, and at least one special character',
        'default' => ':attribute format is invalid',
    ],
    'confirmed' => ':attribute confirmation does not match',
    'same' => ':attribute confirmation does not match',
    'required_if' => ':attribute is required',
    'required_without' => ':attribute is required',
    'enum' => 'The selected :attribute is invalid',

    // Detailed custom messages only for 'file' and 'items'
    'custom' => [
        'file' => [
            'mimetypes' => ':attribute must be a file of type: :values.',
            'max' => ':attribute size may not be greater than :max KB.',
        ],
        'files' => [
            'array' => ':attribute must be an array.',
            'max' => ':attribute may not have more than :max files.',
        ],
        'file_path' => [
            'exists' => ':attribute does not exist',
        ],
        'password' => [
            'regex' => 'Password must be 8-20 characters and contain letters and numbers.',
        ],
        'items' => [
            'required' => 'Cart items cannot be empty',
            'array' => 'Cart items format is invalid',
            'min' => 'At least one cart item is required',
            '*.product_specification_id.required' => 'Product specification ID is required',
            '*.product_specification_id.integer' => 'Product specification ID must be an integer',
            '*.product_specification_id.min' => 'Product specification ID must be greater than 0',
            '*.product_specification_id.exists' => 'Product specification does not exist',
            '*.quantity.required' => 'Product quantity is required',
            '*.quantity.integer' => 'Product quantity must be an integer',
            '*.quantity.min' => 'Product quantity must be greater than 0',
        ],
    ],

    // Attributes array to translate field names to friendly English names
    'attributes' => [
        'name' => 'Name',
        'phone' => 'Phone',
        'email' => 'Email',
        'line' => 'Line',
        'address' => 'Address',
        'title' => 'Title',
        'content' => 'Content',
        'file' => 'Attachment',
        'file_path' => 'File Path',
        'password' => 'Password',
        'password_confirmation' => 'Password Confirmation',
        'old_password' => 'Old Password',
        'items' => 'Cart Items',
        'product_specification_id' => 'Product Specification ID',
        'quantity' => 'Product Quantity',
        'payment_method' => 'Payment Method',
        'shipping_method' => 'Shipping Method',
        'store_address_id' => 'Store Address ID',
        'country_code' => 'Country Code',
        'state' => 'State',
        'district' => 'District',
        'postal_code' => 'Postal Code',
        'address_line1' => 'Address Line 1',
        'address_line2' => 'Address Line 2',
        'invoice_method' => 'Invoice Method',
        'carrier_value' => 'Carrier Number',
        'invoice_address' => 'Invoice Address',
        'vat' => 'VAT Number',
        'invoice_title' => 'Invoice Title',
        'love_code' => 'Love Code',
    ],
];
