<?php

return [
    'buttons' => [
        'filter'            => 'àlẹmọ',
        'clear_all_filters' => 'Clear all',
    ],
    'labels' => [
        'action'           => 'Awọn I<PERSON>e',
        'results_per_page' => 'Esi Ni oju-iwe',
        'clear_filter'     => 'Ko àlẹmọ',
        'no_data'          => 'Ko si awọn igbasilẹ ti a rii',
        'all'              => 'Gbogbo e',
        'selected'         => 'Oun To Mu',
        'filtered'         => 'Filter',
    ],
    'placeholders' => [
        'search' => 'Wa...',
        'select' => 'Mu akoko Kan',
    ],
    'pagination' => [
        'showing' => 'A<PERSON>han',
        'to'      => 'Si',
        'of'      => 'ti',
        'results' => 'Esi',
        'all'     => 'Gbogbo e',
    ],
    'multi_select' => [
        'select' => 'Mu',
        'all'    => 'Gbogbo e',
    ],
    'select' => [
        'select' => 'Mu',
        'all'    => 'Gbogbo e',
    ],
    'boolean_filter' => [
        'all' => 'Gbogbo e',
    ],
    'input_text_options' => [
        'is'           => 'Ni',
        'is_not'       => 'kiise',
        'contains'     => 'Ninu',
        'contains_not' => 'Ko Si Ninu',
        'starts_with'  => 'Bere Pelu',
        'ends_with'    => 'Pari Pelu',
        'is_null'      => 'is_null',
        'is_not_null'  => 'is_not_null',
        'is_blank'     => 'is_blank',
        'is_not_blank' => 'is_not_blank',
        'is_empty'     => 'is_empty',
        'is_not_empty' => 'is_not_empty',
    ],
    'export' => [
        'exporting' => 'jọwọ duro!',
        'completed' => 'O ti setan! Awọn faili rẹ ti šetan fun igbasilẹ',
    ],
    'soft_deletes' => [
        'message_with_trashed' => 'Displaying all records, including deleted ones.',
        'message_only_trashed' => 'Displaying only deleted records.',
        'without_trashed'      => 'Without deleted',
        'with_trashed'         => 'With deleted',
        'only_trashed'         => 'Only deleted',
    ],
    'multi_sort' => [
        'message' => 'Multiple sort is active',
    ],
    'buttons_macros' => [
        'confirm' => [
            'message' => 'Are you sure you want to perform this action?',
        ],
        'confirm_prompt' => [
            'message' => "Are you sure you want to perform this action? \n\n Enter :confirmValue to confirm.",
        ],
    ],
];
