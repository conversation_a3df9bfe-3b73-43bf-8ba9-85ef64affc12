<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseIsEnabled;

return new class extends Migration
{
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseIsEnabled;

    public function up(): void
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->string('code')->unique()->comment('業務編號');
            $table->string('name');
            self::getDatabaseColumnsIsEnabled($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sales');
    }
};
