<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{
    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('specializable_product_specifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedMediumInteger('product_specification_id');
            $table->foreign('product_specification_id', 'product_specifications_fk')
                ->references('id')
                ->on('product_specifications')
                ->onDelete('cascade');


            $table->morphs('specializable', 'specializable_fk');
            $table->decimal('special_price', 8, 2)->nullable();

            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `specializable_product_specifications` COMMENT = '客戶/會員產品規格專屬價格表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('specializable_product_specifications');
    }
};
