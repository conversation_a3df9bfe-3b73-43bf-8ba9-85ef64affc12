<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseContent;


    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!config('cs.customer_visible')) {
            Log::info("Skipping creation of customers table as CS_CUSTOMER_VISIBLE is set to false");
            return;
        }

        Schema::create('customers', function (Blueprint $table)
        {
            $table->mediumIncrements('id')->comment('會員ID');
            $table->string('name', 50)->nullable();
            $table->string('email', 70)->nullable()->unique();
            $table->string('phone', 20)->nullable()->comment('電話');
            $table->string('password', 256)->comment('密碼');
            $table->timestamp('email_verified_at')->nullable();

            // 獎勵點數
            $table->unsignedInteger('reward_points')->default(0)->comment('獎勵點數');


            self::getDatabaseColumnsEditor($table);
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
