<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseContent;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    public function up(): void
    {
        $table = 'reward_category_translations';

        Schema::create($table, function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->smallInteger('reward_category_id')->unsigned();
            $table->foreign('reward_category_id')->references('id')->on('reward_categories')->onDelete('cascade');
            $table->string('lang', 10);
            $table->string('title', 40);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE $table COMMENT = '獎勵類別翻譯內容'");
    }

    public function down(): void
    {
        Schema::dropIfExists('reward_category_translations');
    }
};
