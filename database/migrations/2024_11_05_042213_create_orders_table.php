<?php

use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

class CreateOrdersTable extends Migration
{

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;

    public function up(): void
    {
        $table = 'orders';

        Schema::create('orders', function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement()->comment('主鍵');

            // 關聯會員 如果有的話
            $table->string('orderable_type')->nullable()->comment('訂單所有者類型');
            $table->unsignedBigInteger('orderable_id')->nullable()->comment('訂單所有者ID');

            // 訂購人資訊
            $table->string('name', 100)->nullable()->comment('姓名');
            $table->string('phone', 20)->nullable()->comment('電話');
            $table->string('email', 100)->nullable()->comment('電子郵件');

            // 金額
            $table->string('order_key', 50)->unique()->comment('訂單編號');
            $table->decimal('item_amount', 10, 2)->comment('商品總金額');
            $table->decimal('shipping_cost', 10, 2)->comment('運費');
            $table->decimal('total_amount_untaxed', 10, 2)->comment('未稅總金額');
            $table->decimal('total_amount_taxed', 10, 2)->comment('含稅總金額');

            // 狀態
            $table->string('bk_rtn_code', 20)->nullable()->comment('後端交易通知回應碼');
            $table->string('ft_rtn_code',20)->nullable()->comment('前端交易通知回應碼');
            $table->string('status', 20)->default(EnumOrderStatus::PENDING->value)->comment('訂單狀態');
            $table->string('payment_method', 20)->comment('付款方式');
            $table->string('payment_status', 20)->default('unpaid')->comment('付款狀態');
            $table->string('shipping_method', 20)->comment('運送方式');
            $table->string('shipping_status', 20)->default('unshipped')->comment('配送狀態');
            $table->string('payment_gateway',30)->nullable()->comment('金流平台');
            $table->boolean('released')->default(false)->comment('是否已經釋放預留量');

            // 地址資訊
            $table->smallInteger('store_address_id')->unsigned()->nullable()->comment('商店地址ID');
            $table->foreign('store_address_id')->references('id')->on('store_addresses')->onDelete('set null');
            $table->string('country_code',3)->nullable()->default('TWN')->comment('iso 三位國碼');
            $table->string('state',30)->nullable()->default('台灣')->comment('州/省/縣');
            $table->string('city',30)->nullable()->comment('城市');
            $table->string('district',30)->nullable()->comment('區/鄉/鎮');
            $table->string('postal_code',10)->nullable()->comment('郵遞區號');
            $table->string('address_line1',100)->nullable()->comment('地址行1');
            $table->string('address_line2',100)->nullable()->comment('地址行2');

            // 發票資訊
            $table->string('invoice_method', 20)->comment('發票開立方式：mobile_barcode=手機條碼載具, citizen_card=自然人憑證載具, duplicate=二聯發票, triplicate=三聯發票, donation=捐贈');
            $table->string('carrier_value', 50)->nullable()->comment('載具號碼（載具類型時使用）');
            $table->string('invoice_address', 255)->nullable()->comment('發票地址（二聯/三聯發票時使用）');
            $table->string('vat', 20)->nullable()->comment('統一編號（可選）');
            $table->string('invoice_title', 255)->nullable()->comment('客戶發票標題（可選）');
            $table->string('love_code', 20)->nullable()->comment('愛心碼（捐贈發票時使用');
            $table->json('payload')->nullable()->comment('金流回傳欄位');
            $table->text('remark')->nullable()->comment('備註');

            // 非即時付款的資訊
            $table->string('bank_code', 3)->nullable()->comment('繳費銀行代碼');
            $table->string('v_account', 20)->nullable()->comment('虛擬帳號');
            $table->string('payment_no', 20)->nullable()->comment('繳費代碼（CVS時使用，BARCODE時為空白）');
            $table->datetime('expire_date')->nullable()->comment('繳費期限');
            $table->string('barcode1', 20)->nullable()->comment('條碼第一段號碼（代碼時為空白）');
            $table->string('barcode2', 20)->nullable()->comment('條碼第二段號碼（代碼時為空白）');
            $table->string('barcode3', 20)->nullable()->comment('條碼第三段號碼（代碼時為空白）');

            self::getDatabaseColumnsEditor($table);
            $table->timestamps(); // 建立與更新時間
        });

        DB::statement("ALTER TABLE `$table` COMMENT = '訂單資料表'");

    }

    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
}
