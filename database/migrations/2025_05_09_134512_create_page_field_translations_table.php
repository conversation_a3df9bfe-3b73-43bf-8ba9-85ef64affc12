<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'page_field_translations';

        Schema::create($table, function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $table->smallInteger('page_field_id')->unsigned();
            $table->foreign('page_field_id')->references('id')->on('page_fields')->onDelete('cascade');
            $this->addContentField($table);
            $this->addImageFields($table);
            $this->addLanguageField($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE $table COMMENT = '頁面欄位翻譯'");

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('page_field_translations');
    }
};
