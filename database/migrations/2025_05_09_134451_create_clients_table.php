<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!config('cs.client_visible')) {
            Log::info("Skipping creation of clients table as CS_CLIENT_VISIBLE is set to false");
            return;
        }

        Schema::create('clients', function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();

            // 客戶基本資料
            $table->string('email', 70)->unique()->comment('電子郵件');
            $table->string('password', 256)->comment('網路登錄密碼');
            $table->string('code', 30)->nullable()->unique()->index()->comment('客戶代號');
            $table->string('name', 50)->nullable()->index()->comment('客戶簡稱');


            // 聯絡資訊
            $table->string('phone1', 20)->nullable()->index()->comment('客戶電話1');
            $table->string('phone2', 20)->nullable()->index()->comment('客戶電話2');
            $table->string('phone1_extension', 10)->nullable()->comment('電話1分機');
            $table->string('phone2_extension', 10)->nullable()->comment('電話2分機');
            $table->string('fax', 20)->nullable()->comment('傳真電話');
            $table->string('mobile', 20)->nullable()->comment('手機');
            $table->string('contact_person', 50)->nullable()->comment('聯絡人');


            // 送貨與發票地址
            $table->string('invoice_title', 150)->nullable()->comment('客戶發票標題');
            $table->string('invoice_address', 255)->nullable()->comment('發票地址');
            $table->string('address', 255)->nullable()->comment('客戶地址');

            self::getDatabaseColumnsEditor($table);
            // 時間戳記
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
