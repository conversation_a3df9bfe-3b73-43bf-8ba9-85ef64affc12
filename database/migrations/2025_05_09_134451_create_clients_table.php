<?php

use App\Enum\EnumPriceType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!config('cs.client_visible')) {
            Log::info("Skipping creation of clients table as CS_CLIENT_VISIBLE is set to false");
            return;
        }

        Schema::create('clients', function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();

            // 必填欄位
            $table->string('email', 70)->nullable()->unique()->comment('電子郵件');
            $table->string('password', 256)->comment('網路登錄密碼');

            // 客戶基本資料
            $table->string('code', 30)->nullable()->unique()->index()->comment('客戶代號');
            $table->string('name', 50)->nullable()->index()->comment('客戶簡稱');
            $table->string('invoice_title', 150)->nullable()->comment('客戶發票標題');
            $table->string('address', 255)->nullable()->comment('客戶地址');
            $table->string('invoice_address', 255)->nullable()->comment('發票地址');

            // 聯絡資訊
            $table->string('phone1', 20)->nullable()->index()->comment('客戶電話1');
            $table->string('phone2', 20)->nullable()->index()->comment('客戶電話2');
            $table->string('phone1_extension', 10)->nullable()->comment('電話1分機');
            $table->string('phone2_extension', 10)->nullable()->comment('電話2分機');
            $table->string('fax', 20)->nullable()->comment('傳真電話');
            $table->string('mobile', 20)->nullable()->comment('手機');
            $table->string('contact_person', 50)->nullable()->comment('聯絡人');
            $table->string('company_name', 60)->nullable()->comment('客戶名稱');


            // 稅務和發票資訊
            $table->string('vat', 20)->nullable()->index()->comment('統一編號');

            // 地區和郵遞區號
            $table->string('area', 50)->nullable()->comment('所屬區域');
            $table->string('zipcode', 10)->nullable()->comment('郵遞區號');

            // 個人資訊
            $table->string('id_number', 20)->nullable()->unique()->comment('身份證號');
            $table->date('birthday')->nullable()->comment('出生日期');


            // 發票和付款方式
            $table->string('invoice_issue_method', 30)->nullable()->comment('發票開立方式');
            $table->string('invoice_tax_type', 30)->nullable()->comment('發票開立稅別');
            $table->tinyInteger('gender')->nullable()->default(0)->comment('性別'); // 0: 未指定, 1: 男, 2: 女
            // 新增
            $table->string('web_account', 30)->nullable()->comment('網路登錄帳號');
            $table->string('sale_code', 30)->nullable()->default('0609')->comment('業務員代號');
            $table->boolean('email_statement')->default(false)->comment('EMail對帳單');
            $table->decimal('basic_shipping_fee', 10, 2)->nullable()->comment('基本運費');
            $table->decimal('free_shipping_threshold', 10, 2)->nullable()->comment('免運門檻金額');
            $table->string('carrier_number', 30)->nullable()->comment('載具號碼');
            $table->string('price_type', 20)->default(EnumPriceType::PRICE_4->value)->comment('售價類型');
            $table->string('billing_address', 255)->nullable()->comment('帳單地址');
            $table->decimal('bonus_points',10,2)->default(0)->comment('紅利點數');
            $table->unsignedInteger('pos_bonus')->default(0)->comment('POS紅利點數');
            $table->timestamp('email_verified_at')->nullable();
            $table->text('payload')->nullable()->comment('ERP 回傳訊息');
            $table->unsignedTinyInteger('payload_status')->nullable()->comment('0:失敗，1:成功');

            self::getDatabaseColumnsEditor($table);
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
