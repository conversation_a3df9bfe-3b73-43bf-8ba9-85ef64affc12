<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'product_shipping_method';

        Schema::create($table, function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();

            $table->mediumInteger('product_id')->unsigned()->comment('產品ID');
            $table->foreign('product_id')
                ->references('id')
                ->on('products')
                ->onDelete('cascade');

            $table->tinyInteger('shipping_method_id')->unsigned()->comment('運送方式ID');
            $table->foreign('shipping_method_id')
                ->references('id')
                ->on('shipping_methods')
                ->onDelete('cascade');

            $table->unsignedTinyInteger('status')->default(1)->comment('狀態，0 是關閉，1 是開啟');
            $table->unsignedTinyInteger('combine_same_product')->default(0)->comment('狀態，0 是關閉，1 是開啟');
            $table->unsignedTinyInteger('combine_different_product')->default(0)->comment('狀態，0 是關閉，1 是開啟');
            $table->unsignedSmallInteger('amount')->default(0)->comment('運費金額');

            self::getDatabaseColumnsEditor($table);
            $table->timestamps();

            // 確保同一產品和運送方式的組合是唯一的
            $table->unique(['product_id', 'shipping_method_id']);
        });

        DB::statement("ALTER TABLE `$table` COMMENT = '產品運送方式關聯表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_shipping_method');
    }
};
