<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('tag_pivot')) {
            return;
        }

        Schema::create('tag_pivot', function (Blueprint $table)
        {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $table->smallInteger('tag_id')->unsigned();
            $table->foreign('tag_id')->references('id')->on('tags')->onDelete('cascade');
            $table->morphs('taggable');
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `tag_pivot` COMMENT = 'tag_pivot'");

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tag_pivot');
    }
};
