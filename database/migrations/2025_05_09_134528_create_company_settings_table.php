<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'company_settings';

        Schema::create($table, function (Blueprint $table) {
            $table->tinyInteger('id')->unsigned()->autoIncrement();

            $table->string('lang', 10)->comment('語言類別代碼')->unique();
            $this->addLogoField($table);
            $table->string('name',80);
            $table->text('description',)->nullable();
            $table->string('vat',15)->nullable();
            $table->string('address_1',100)->nullable();
            $table->string('address_2',100)->nullable();
            $table->string('phone_1',20)->nullable();
            $table->string('phone_2',20)->nullable();
            $table->string('email_1',70)->nullable();
            $table->string('email_2',70)->nullable();
            $table->string('line_link',1024)->nullable();
            $table->string('fb_link',1024)->nullable();
            $table->string('ig_link',1024)->nullable();
            $table->string('twitter_link',1024)->nullable();
            $table->string('threads_link',1024)->nullable();
            $table->string('tg_link',1024)->nullable();

            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE $table COMMENT = '公司基本設定'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_settings');
    }
};
