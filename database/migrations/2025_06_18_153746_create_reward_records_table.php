<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseAdmin;

    public function up(): void
    {
        $table = 'reward_records';

        Schema::create($table, function (Blueprint $table) {
            $table->id();
            $table->morphs('rewardable');
            $table->smallInteger('reward_category_id')->unsigned();
            $table->foreign('reward_category_id')->references('id')->on('reward_categories')->onDelete('cascade');
            $table->unsignedInteger('old_points');
            $table->unsignedInteger('new_points');
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE $table COMMENT = '獎勵記錄'");
    }

    public function down(): void
    {
        Schema::dropIfExists('reward_records');
    }
};
