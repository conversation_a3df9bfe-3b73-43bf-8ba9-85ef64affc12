<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSEO;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSEO;
    use CCTraitDatabaseOG;


    public function up(): void
    {
        $table = 'article_category_translations';

        if (!config('cs.article_visible')) {
            Log::info("Skipping creation of $table table as CS_ARTICLE_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $this->addTitleField($table);
            $this->addContentField($table);
            self::getDatabaseColumnsSEO($table);
            self::getDatabaseColumnsOG($table);
            $this->addLanguageField($table);
            $table->mediumInteger('article_category_id')->unsigned();
            $table->foreign('article_category_id')->references('id')->on('article_categories')->onDelete('cascade');
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE $table COMMENT = '文章類型翻譯內容'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('article_category_translations');
    }
};
