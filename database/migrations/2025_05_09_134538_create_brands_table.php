<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSort;

return new class extends Migration {

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSort;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'brands';

        if (!config('cs.brand_visible')) {
            Log::info("Skipping creation of $table table as CS_BRAND_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $this->addLogoField($table);
            $this->addKeyField($table);
            self::getDatabaseColumnsSort($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `$table` COMMENT = '合作品牌表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brands');
    }
};
