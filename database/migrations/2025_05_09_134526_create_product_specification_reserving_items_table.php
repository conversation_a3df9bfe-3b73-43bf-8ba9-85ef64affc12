<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSEO;

return new class extends Migration {

    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'product_specification_reserving_items';

        Schema::create('product_specification_reserving_items', function (Blueprint $table) {

            $table->mediumInteger('id')->unsigned()->autoIncrement()->comment('主鍵');
            $table->mediumInteger('product_specification_id')->unsigned()->comment('產品規格ID');
            $table->foreign('product_specification_id','product_specification_id_foreign')->references('id')->on('product_specifications')->onDelete('cascade');
            $table->unsignedInteger('quantity')->comment('預留數量');

            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `$table` COMMENT = '產品規格預留項目表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_specification_reserving_items');
    }
};
