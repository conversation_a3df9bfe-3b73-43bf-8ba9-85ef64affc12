<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseAdmin;

    public function up(): void
    {
        $table = 'reward_categories';

        Schema::create($table, function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE $table COMMENT = '獎勵類別'");
    }

    public function down(): void
    {
        Schema::dropIfExists('reward_categories');
    }
};
