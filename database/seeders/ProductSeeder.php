<?php

namespace Database\Seeders;

use App\Models\ProductSpecification;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductDetailType;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;

class ProductSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        $path = __DIR__ . '/../files/pfl.csv';

        if (!file_exists($path)) {
            throw new \Exception("CSV 檔案不存在: {$path}");
        }

        $imageJsonPath = __DIR__ . '/../files/images_structure.json';
        $imageJsonContent = File::get($imageJsonPath);
        $imageJsonContent = json_decode($imageJsonContent, true);

        $handle = fopen($path, 'r');
        $header = fgetcsv($handle);

        while (($row = fgetcsv($handle)) !== false) {
            $row = array_combine($header, $row);
            $status = $row['持續上架'] == 1 ? EnumStatus::ENABLE : EnumStatus::DISABLE;
            $category = $this->createCategory($row);
            $images = $imageJsonContent[$row['產品代號']]['files'] ?? [];
            $product = $this->createProduct($row, $category, $images,$status);
            $spec = $this->createProductSpecification($row, $product);
        }

        fclose($handle);
    }

    public function createCategory(array $data): ProductCategory
    {

        $lv1 = ProductCategory::query()->firstOrCreate([
            'code' =>  str_pad($data['大類類別代號'], 2, '0', STR_PAD_LEFT),
        ], [
            'code' =>  str_pad($data['大類類別代號'], 2, '0', STR_PAD_LEFT),
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv1->translations()->firstOrCreate([
            'product_category_id' => $lv1->id,
            'title' => $data['大類類別名稱'],
        ], [
            'product_category_id' => $lv1->id,
            'title' => $data['大類類別名稱'],
            'lang' => 'zh_TW',
        ]);


        $lv2 = ProductCategory::query()->firstOrCreate([
            'code' =>   str_pad($data['中類類別代號'], 2, '0', STR_PAD_LEFT),
            'parent_id' => $lv1->id,
        ], [
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv2->translations()->firstOrCreate([
            'product_category_id' => $lv2->id,
            'title' => $data['中類類別名稱'],
        ], [
            'product_category_id' => $lv2->id,
            'title' => $data['中類類別名稱'],
            'lang' => 'zh_TW',
        ]);

        if (empty($data['小類類別代號'])) {
            return $lv2;
        }

        $lv3 = ProductCategory::query()->firstOrCreate([
            'code' => str_pad($data['小類類別代號'], 2, '0', STR_PAD_LEFT),
            'parent_id' => $lv2->id,
        ], [
            'type' =>  EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv3->translations()->firstOrCreate([
            'product_category_id' => $lv3->id,
            'title' => $data['小類類別名稱'],
        ], [
            'product_category_id' => $lv3->id,
            'title' => $data['小類類別名稱'],
            'lang' => 'zh_TW',
        ]);

        if (empty($data['細類類別代號'])) {
            return $lv3;
        }

        $lv4 = ProductCategory::query()->firstOrCreate([
            'code' => str_pad($data['細類類別代號'], 2, '0', STR_PAD_LEFT),
            'parent_id' => $lv3->id,
        ], [
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv4->translations()->firstOrCreate([
            'product_category_id' => $lv4->id,
            'title' => $data['細類類別名稱'],
        ], [
            'product_category_id' => $lv4->id,
            'title' => $data['細類類別名稱'],
            'lang' => 'zh_TW',
        ]);

        if (empty($data['細項類別代號'])) {
            return $lv4;
        }

        $lv5 = ProductCategory::query()->firstOrCreate([
            'code' => str_pad($data['細項類別代號'], 2, '0', STR_PAD_LEFT),
            'parent_id' => $lv4->id,
        ], [
            'type' => EnumProductType::Product,
            'is_hottest' => false,
            'status' => EnumStatus::ENABLE->value,
        ]);

        $lv5->translations()->firstOrCreate([
            'product_category_id' => $lv5->id,
            'title' => $data['細項類別名稱'],
        ], [
            'product_category_id' => $lv5->id,
            'title' => $data['細項類別名稱'],
            'lang' => 'zh_TW',
        ]);

        return $lv5;
    }

    public function createProduct(array $data, ProductCategory $category, array $images,EnumStatus $status): Product
    {
        $product = Product::query()->firstOrCreate([
            'part_number' => $data['產品代號'],
            'product_category_id' => $category->id,
        ], [
            'type' => EnumProductType::Product->value,
            'is_hottest' => false,
            'is_newest' => false,
            'status' => $status->value,
        ]);


        $product->translations()->create([
            'title' => $data['產品名稱'],
            'content_1' => $data['簡要說明1'],
            'content_2' => $data['簡要說明2'],
            'content_3' => $data['簡要說明3'],
            'lang' => 'zh_TW',
            'product_id' => $product->id,
        ]);


        $detail = $product->details()->create([
            'product_id' => $product->id,
            'type' => EnumProductDetailType::DESCRIPTION->value,
        ]);

        $text = "產品代號 : {$data['產品代號']}\n" .
            "產品條碼 : {$data['CODE128']}\n" .
            "產品名稱 : {$data['產品名稱']}\n" .
            "產品類別 : {$category->translations->where('lang', 'zh_TW')->first()->title}\n" .
            "材質 : {$data['材質']}\n" .
            "款式 : {$data['顏色(規格1)']}\n" .
            "尺寸 : {$data['尺寸(規格2)']}\n" .
            "規格 : {$data['包裝方式']}\n" .
            "產地 : {$data['產地國別']}\n" .
            "建議售價 : {$data['建議售價']}\n" .
            "{$data['簡要說明3']}";


        $detail->translations()->create([
            'content' => $text,
            'lang' => 'zh_TW',
        ]);


        foreach ($images as $image) {
            $product->images()->create([
                'is_default' => false,
                'image' => "image/{$data['產品代號']}/{$image}",
                'image_mobile' => "image/{$data['產品代號']}/compressed_{$image}",
            ]);
        }


        return $product;
    }

    public function createProductSpecification(array $data, Product $product): ProductSpecification
    {
        $spec = ProductSpecification::query()->create([
            'listing_price' => $data['建議售價'],
            'selling_price' => $data['銷售單價1'],
            'selling_price2' => $data['銷售單價2'],
            'selling_price3' => $data['銷售單價3'],
            'selling_price4' => $data['銷售單價4'],
           // 'selling_price5' => $data['銷售單價5'],
            'inventory' => 0,
            'sku' => $data['產品代號'],
            'product_id' => $product->id,
            'status' => EnumStatus::ENABLE->value,
        ]);


        $spec->translations()->create([
            'title' => $data['產品名稱'],
            'lang' => 'zh_TW',
        ]);

        return $spec;
    }
}
