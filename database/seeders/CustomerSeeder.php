<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;

class CustomerSeeder extends Seeder
{
    protected $output = null;
    protected $requiredFields = ['code', 'password'];
    protected $booleanFields = [
        'email_statement'
    ];
    protected $integerFields = [
        'area', 'invoice_issue_method', 'invoice_tax_type', 'gender'
    ];
    protected $decimalFields = [
        'basic_shipping_fee', 'free_shipping_threshold'
    ];

    public function setOutput($output)
    {
        $this->output = $output;
        return $this;
    }

    protected function info($message)
    {
        if ($this->output) {
            $this->output->info($message);
        } elseif (isset($this->command)) {
            $this->command->info($message);
        } else {
            echo "[INFO] {$message}\n";
        }
    }

    protected function error($message)
    {
        if ($this->output) {
            $this->output->error($message);
        } elseif (isset($this->command)) {
            $this->command->error($message);
        } else {
            echo "[ERROR] {$message}\n";
        }
    }

    public function run(): void
    {
        $path = __DIR__ . '/../../database/files/auth.csv';


        $this->seedingDefaultCustomer();

        if (!file_exists($path)) {
            Log::error("CSV file does not exist: {$path}");
            $this->error("CSV file does not exist: {$path}");
            return;
        }

        $this->info('Importing customers from CSV file...');

        // Read the CSV file
        $file = fopen($path, 'r');
        $headers = fgetcsv($file);
        $columnMap = $this->getColumnMap();
        $columnIndexes = $this->getColumnIndexes($headers, $columnMap);

        $count = 0;
        $skipped = 0;
        $errors = [];

        // Process each row
        while (($row = fgetcsv($file)) !== false) {
            $customerData = [];
            $customerCode = isset($row[$columnIndexes['code']]) ? trim($row[$columnIndexes['code']]) : 'unknown';

            // Skip if customer code doesn't start with '2'
            if (!str_starts_with($customerCode, '2')) {
                $skipped++;
                continue;
            }

            // Process each column
            foreach ($columnIndexes as $dbColumn => $index) {
                if (!isset($row[$index])) continue;

                $value = trim($row[$index]);

                // Skip empty values except required fields
                if ($value === '') {
                    if (in_array($dbColumn, $this->requiredFields)) {
                        $errors[$customerCode][] = "Required field '{$dbColumn}' is empty";
                        continue;
                    } else {
                        continue; // Skip empty non-required fields
                    }
                }

                // Process value based on field type
                $value = $this->processFieldValue($dbColumn, $value);
                $customerData[$dbColumn] = $value;
            }

            if(!empty($errors[$customerCode])) continue;

            if(!empty($customerData['email'])){
                $existsEmail = DB::table('customers')
                    ->where('email', $customerData['email'])
                    ->exists();
                if($existsEmail){
                    $errors[$customerCode][] = "Email already exists";
                    continue;
                }

            }
            // Insert or update customer
            $exists = DB::table('customers')
                    ->where('code', $customerData['code'])
                    ->exists();


            if (!$exists) {
                DB::table('customers')->insert($customerData);
            } else {
                DB::table('customers')
                    ->where('code', $customerData['code'])
                    ->update($customerData);
            }
            $count++;
        }

        fclose($file);
        $this->logErrors($errors, $skipped);
        $this->info("Import completed: {$count} customers imported, {$skipped} skipped.");
    }

    public function seedingDefaultCustomer(): void
    {
        $account = config('cs.customer.app_super_customer_account');
        $password = config('cs.customer.app_super_customer_password');

        Customer::factory()->create([
            'email' => $account,
            'password' => $password,
        ]);
    }

    protected function getColumnMap(): array
    {
        return [
            '客戶代號' => 'code',
            '客戶簡稱' => 'name',
            '客戶名稱' => null,
            '客戶發票抬頭' => 'invoice_title',
            '客戶地址' => 'address',
            '發票地址' => 'invoice_address',
            '客戶電話1' => 'phone1',
            '客戶電話2' => 'phone2',
            '電話1分機' => 'phone1_extension',
            '電話2分機' => 'phone2_extension',
            '傳真電話' => 'fax',
            '手機' => 'mobile',
            '聯絡人' => 'contact_person',
            '統一編號' => 'vat',

            '業務員代號' => 'sale_code',

            '電子郵件' => 'email',
            'EMail對帳單' => 'email_statement',

            '網路登錄帳號' => 'web_account',
            '網路登錄密碼' => 'password',
            '所屬區域' => 'area',
            '郵遞區號' => 'zipcode',

            '身份證號' => 'id_number',
            '出生日期' => 'birthday',

            '發票開立方式' => 'invoice_issue_method',
            '發票開立稅別' => 'invoice_tax_type',

            '性別' => 'gender',

            '基本運費' => 'basic_shipping_fee',
            '免運門檻金額' => 'free_shipping_threshold',


            '載具號碼' => 'carrier_number',


        ];
    }

    protected function getColumnIndexes(array $headers, array $columnMap): array
    {
        $columnIndexes = [];
        foreach ($columnMap as $csvHeader => $dbColumn) {
            $index = array_search($csvHeader, $headers);
            if ($index !== false && $dbColumn !== null) {
                $columnIndexes[$dbColumn] = $index;
            }
        }
        return $columnIndexes;
    }

    protected function processFieldValue(string $dbColumn, $value)
    {
        // Process password
        if ($dbColumn === 'password') {
            return Hash::make($value);
        }

        // Process boolean fields
        if (in_array($dbColumn, $this->booleanFields)) {
            return in_array(strtolower($value), ['1', 'true', 'yes', 'y', 'on']);
        }

        // Process integer fields
        if (in_array($dbColumn, $this->integerFields) && is_numeric($value)) {
            return (int)$value;
        }

        // Process decimal fields
        if (in_array($dbColumn, $this->decimalFields)) {
            return (float)str_replace(',', '', $value);
        }

        // Process date fields
        if ($dbColumn === 'birthday' && !empty($value)) {
            try {
                $date = \DateTime::createFromFormat('Y/m/d', $value);
                return $date ? $date->format('Y-m-d') : null;
            } catch (\Exception $e) {
                return null;
            }
        }

        return $value;
    }


    protected function logErrors(array $errors, int $skipped): void
    {
        if (empty($errors)) return;

        $errorLog = "Errors during client import:\n";
        foreach ($errors as $customerCode => $customerErrors) {
            $errorLog .= "Client {$customerCode}: " . implode(", ", $customerErrors) . "\n";
        }

        Log::error($errorLog);
        $this->error("There were {$skipped} errors during import. Check the log for details.");
    }
}
