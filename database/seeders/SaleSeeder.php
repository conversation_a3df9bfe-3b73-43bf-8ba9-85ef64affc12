<?php

namespace Database\Seeders;

use App\Models\Sale;
use Illuminate\Database\Seeder;

class SaleSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $sales = [
            [
                'code' => 'SP001',
                'name' => '王小明',
            ],
            [
                'code' => 'SP002',
                'name' => '李小華',
            ],
            [
                'code' => 'SP003',
                'name' => '張小美',
            ],
            [
                'code' => 'SP004',
                'name' => '陳小強',
            ],
            [
                'code' => 'SP005',
                'name' => '林小芳',
            ],
        ];

        foreach ($sales as $sale) {
            Sale::query()->firstOrCreate(
                ['code' => $sale['code']],
                $sale
            );
        }
    }
}
